<?php
/*
Plugin Name: rss-plugin-slugs
Description: Chatr.tv rss slugs zoznam wordpress >= 4.4
Version:     1.6
Author:      MW
*/

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

function registerSlugsEndpoint() {
    add_rewrite_endpoint('chatr_slugs', EP_ALL);
}

register_deactivation_hook( __FILE__, 'flush_rewrite_rules');
register_activation_hook( __FILE__, 'my2plugin_flush_rewrites');

function my2plugin_flush_rewrites() {
    // call your CPT registration function here (it should also be hooked into 'init')
    registerSlugsEndpoint();
    flush_rewrite_rules();
}

// add rewrite rule for tags endpoint
add_action('init', 'registerSlugsEndpoint', 1000);

// send tags as XML if chatr-tags endpoint is visited
add_action('template_redirect', function() {
    global $wp_query, $wpdb;

    if (!isset($wp_query->query_vars['chatr_slugs'])) {
        return;
    }

    $tags = $wpdb->get_results("
        select name,slug from $wpdb->term_taxonomy t join $wpdb->term_relationships r on t.term_taxonomy_id=r.term_taxonomy_id join $wpdb->terms e on e.term_id=t.term_id where taxonomy='post_tag' and count>1 group by name,slug limit 10000
    ", ARRAY_A);

    // load priorities for tags
    header("Content-Type: application/xhtml+xml; charset=utf-8");
    echo("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
    echo("<tags>\n");

    foreach ($tags as $tag) {
        echo (
            "\t<tag name='".htmlspecialchars($tag['name'], ENT_XML1 | ENT_QUOTES)."' slug='".htmlspecialchars($tag['slug'], ENT_XML1 | ENT_QUOTES)."'/>\n"
        );
    }

    echo("</tags>");

    exit;
}, 100);


