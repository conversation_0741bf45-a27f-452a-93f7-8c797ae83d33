<?php return array(
    'root' => array(
        'pretty_version' => 'v1.5.5',
        'version' => '1.5.5.0',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '4973fa9dbadc7cc246e2c6cfab44047133ffd70b',
        'name' => 'wp-graphql/wp-graphql',
        'dev' => false,
    ),
    'versions' => array(
        'ivome/graphql-relay-php' => array(
            'pretty_version' => 'v0.6.0',
            'version' => '0.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ivome/graphql-relay-php',
            'aliases' => array(),
            'reference' => '7055fd45b7e552cee4d1290849b84294b0049373',
            'dev_requirement' => false,
        ),
        'webonyx/graphql-php' => array(
            'pretty_version' => 'v14.9.0',
            'version' => '14.9.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webonyx/graphql-php',
            'aliases' => array(),
            'reference' => '36b83621deb5eae354347a2e86dc7aec81b32a82',
            'dev_requirement' => false,
        ),
        'wp-graphql/wp-graphql' => array(
            'pretty_version' => 'v1.5.5',
            'version' => '1.5.5.0',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '4973fa9dbadc7cc246e2c6cfab44047133ffd70b',
            'dev_requirement' => false,
        ),
    ),
);
