<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'GraphQLRelay\\Connection\\ArrayConnection' => $vendorDir . '/ivome/graphql-relay-php/src/Connection/ArrayConnection.php',
    'GraphQLRelay\\Connection\\Connection' => $vendorDir . '/ivome/graphql-relay-php/src/Connection/Connection.php',
    'GraphQLRelay\\Mutation\\Mutation' => $vendorDir . '/ivome/graphql-relay-php/src/Mutation/Mutation.php',
    'GraphQLRelay\\Node\\Node' => $vendorDir . '/ivome/graphql-relay-php/src/Node/Node.php',
    'GraphQLRelay\\Node\\Plural' => $vendorDir . '/ivome/graphql-relay-php/src/Node/Plural.php',
    'GraphQLRelay\\Relay' => $vendorDir . '/ivome/graphql-relay-php/src/Relay.php',
    'GraphQL\\Deferred' => $vendorDir . '/webonyx/graphql-php/src/Deferred.php',
    'GraphQL\\Error\\ClientAware' => $vendorDir . '/webonyx/graphql-php/src/Error/ClientAware.php',
    'GraphQL\\Error\\DebugFlag' => $vendorDir . '/webonyx/graphql-php/src/Error/DebugFlag.php',
    'GraphQL\\Error\\Error' => $vendorDir . '/webonyx/graphql-php/src/Error/Error.php',
    'GraphQL\\Error\\FormattedError' => $vendorDir . '/webonyx/graphql-php/src/Error/FormattedError.php',
    'GraphQL\\Error\\InvariantViolation' => $vendorDir . '/webonyx/graphql-php/src/Error/InvariantViolation.php',
    'GraphQL\\Error\\SyntaxError' => $vendorDir . '/webonyx/graphql-php/src/Error/SyntaxError.php',
    'GraphQL\\Error\\UserError' => $vendorDir . '/webonyx/graphql-php/src/Error/UserError.php',
    'GraphQL\\Error\\Warning' => $vendorDir . '/webonyx/graphql-php/src/Error/Warning.php',
    'GraphQL\\Exception\\InvalidArgument' => $vendorDir . '/webonyx/graphql-php/src/Exception/InvalidArgument.php',
    'GraphQL\\Executor\\ExecutionContext' => $vendorDir . '/webonyx/graphql-php/src/Executor/ExecutionContext.php',
    'GraphQL\\Executor\\ExecutionResult' => $vendorDir . '/webonyx/graphql-php/src/Executor/ExecutionResult.php',
    'GraphQL\\Executor\\Executor' => $vendorDir . '/webonyx/graphql-php/src/Executor/Executor.php',
    'GraphQL\\Executor\\ExecutorImplementation' => $vendorDir . '/webonyx/graphql-php/src/Executor/ExecutorImplementation.php',
    'GraphQL\\Executor\\Promise\\Adapter\\AmpPromiseAdapter' => $vendorDir . '/webonyx/graphql-php/src/Executor/Promise/Adapter/AmpPromiseAdapter.php',
    'GraphQL\\Executor\\Promise\\Adapter\\ReactPromiseAdapter' => $vendorDir . '/webonyx/graphql-php/src/Executor/Promise/Adapter/ReactPromiseAdapter.php',
    'GraphQL\\Executor\\Promise\\Adapter\\SyncPromise' => $vendorDir . '/webonyx/graphql-php/src/Executor/Promise/Adapter/SyncPromise.php',
    'GraphQL\\Executor\\Promise\\Adapter\\SyncPromiseAdapter' => $vendorDir . '/webonyx/graphql-php/src/Executor/Promise/Adapter/SyncPromiseAdapter.php',
    'GraphQL\\Executor\\Promise\\Promise' => $vendorDir . '/webonyx/graphql-php/src/Executor/Promise/Promise.php',
    'GraphQL\\Executor\\Promise\\PromiseAdapter' => $vendorDir . '/webonyx/graphql-php/src/Executor/Promise/PromiseAdapter.php',
    'GraphQL\\Executor\\ReferenceExecutor' => $vendorDir . '/webonyx/graphql-php/src/Executor/ReferenceExecutor.php',
    'GraphQL\\Executor\\Values' => $vendorDir . '/webonyx/graphql-php/src/Executor/Values.php',
    'GraphQL\\Experimental\\Executor\\Collector' => $vendorDir . '/webonyx/graphql-php/src/Experimental/Executor/Collector.php',
    'GraphQL\\Experimental\\Executor\\CoroutineContext' => $vendorDir . '/webonyx/graphql-php/src/Experimental/Executor/CoroutineContext.php',
    'GraphQL\\Experimental\\Executor\\CoroutineContextShared' => $vendorDir . '/webonyx/graphql-php/src/Experimental/Executor/CoroutineContextShared.php',
    'GraphQL\\Experimental\\Executor\\CoroutineExecutor' => $vendorDir . '/webonyx/graphql-php/src/Experimental/Executor/CoroutineExecutor.php',
    'GraphQL\\Experimental\\Executor\\Runtime' => $vendorDir . '/webonyx/graphql-php/src/Experimental/Executor/Runtime.php',
    'GraphQL\\Experimental\\Executor\\Strand' => $vendorDir . '/webonyx/graphql-php/src/Experimental/Executor/Strand.php',
    'GraphQL\\GraphQL' => $vendorDir . '/webonyx/graphql-php/src/GraphQL.php',
    'GraphQL\\Language\\AST\\ArgumentNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ArgumentNode.php',
    'GraphQL\\Language\\AST\\BooleanValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/BooleanValueNode.php',
    'GraphQL\\Language\\AST\\DefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/DefinitionNode.php',
    'GraphQL\\Language\\AST\\DirectiveDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/DirectiveDefinitionNode.php',
    'GraphQL\\Language\\AST\\DirectiveNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/DirectiveNode.php',
    'GraphQL\\Language\\AST\\DocumentNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/DocumentNode.php',
    'GraphQL\\Language\\AST\\EnumTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/EnumTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\EnumTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/EnumTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\EnumValueDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/EnumValueDefinitionNode.php',
    'GraphQL\\Language\\AST\\EnumValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/EnumValueNode.php',
    'GraphQL\\Language\\AST\\ExecutableDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ExecutableDefinitionNode.php',
    'GraphQL\\Language\\AST\\FieldDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/FieldDefinitionNode.php',
    'GraphQL\\Language\\AST\\FieldNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/FieldNode.php',
    'GraphQL\\Language\\AST\\FloatValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/FloatValueNode.php',
    'GraphQL\\Language\\AST\\FragmentDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/FragmentDefinitionNode.php',
    'GraphQL\\Language\\AST\\FragmentSpreadNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/FragmentSpreadNode.php',
    'GraphQL\\Language\\AST\\HasSelectionSet' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/HasSelectionSet.php',
    'GraphQL\\Language\\AST\\InlineFragmentNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/InlineFragmentNode.php',
    'GraphQL\\Language\\AST\\InputObjectTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/InputObjectTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\InputObjectTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/InputObjectTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\InputValueDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/InputValueDefinitionNode.php',
    'GraphQL\\Language\\AST\\IntValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/IntValueNode.php',
    'GraphQL\\Language\\AST\\InterfaceTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/InterfaceTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\InterfaceTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/InterfaceTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\ListTypeNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ListTypeNode.php',
    'GraphQL\\Language\\AST\\ListValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ListValueNode.php',
    'GraphQL\\Language\\AST\\Location' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/Location.php',
    'GraphQL\\Language\\AST\\NameNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/NameNode.php',
    'GraphQL\\Language\\AST\\NamedTypeNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/NamedTypeNode.php',
    'GraphQL\\Language\\AST\\Node' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/Node.php',
    'GraphQL\\Language\\AST\\NodeKind' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/NodeKind.php',
    'GraphQL\\Language\\AST\\NodeList' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/NodeList.php',
    'GraphQL\\Language\\AST\\NonNullTypeNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/NonNullTypeNode.php',
    'GraphQL\\Language\\AST\\NullValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/NullValueNode.php',
    'GraphQL\\Language\\AST\\ObjectFieldNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ObjectFieldNode.php',
    'GraphQL\\Language\\AST\\ObjectTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ObjectTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\ObjectTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ObjectTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\ObjectValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ObjectValueNode.php',
    'GraphQL\\Language\\AST\\OperationDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/OperationDefinitionNode.php',
    'GraphQL\\Language\\AST\\OperationTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/OperationTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\ScalarTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ScalarTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\ScalarTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ScalarTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\SchemaDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/SchemaDefinitionNode.php',
    'GraphQL\\Language\\AST\\SchemaTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/SchemaTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\SelectionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/SelectionNode.php',
    'GraphQL\\Language\\AST\\SelectionSetNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/SelectionSetNode.php',
    'GraphQL\\Language\\AST\\StringValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/StringValueNode.php',
    'GraphQL\\Language\\AST\\TypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/TypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\TypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/TypeExtensionNode.php',
    'GraphQL\\Language\\AST\\TypeNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/TypeNode.php',
    'GraphQL\\Language\\AST\\TypeSystemDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/TypeSystemDefinitionNode.php',
    'GraphQL\\Language\\AST\\UnionTypeDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/UnionTypeDefinitionNode.php',
    'GraphQL\\Language\\AST\\UnionTypeExtensionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/UnionTypeExtensionNode.php',
    'GraphQL\\Language\\AST\\ValueNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/ValueNode.php',
    'GraphQL\\Language\\AST\\VariableDefinitionNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/VariableDefinitionNode.php',
    'GraphQL\\Language\\AST\\VariableNode' => $vendorDir . '/webonyx/graphql-php/src/Language/AST/VariableNode.php',
    'GraphQL\\Language\\DirectiveLocation' => $vendorDir . '/webonyx/graphql-php/src/Language/DirectiveLocation.php',
    'GraphQL\\Language\\Lexer' => $vendorDir . '/webonyx/graphql-php/src/Language/Lexer.php',
    'GraphQL\\Language\\Parser' => $vendorDir . '/webonyx/graphql-php/src/Language/Parser.php',
    'GraphQL\\Language\\Printer' => $vendorDir . '/webonyx/graphql-php/src/Language/Printer.php',
    'GraphQL\\Language\\Source' => $vendorDir . '/webonyx/graphql-php/src/Language/Source.php',
    'GraphQL\\Language\\SourceLocation' => $vendorDir . '/webonyx/graphql-php/src/Language/SourceLocation.php',
    'GraphQL\\Language\\Token' => $vendorDir . '/webonyx/graphql-php/src/Language/Token.php',
    'GraphQL\\Language\\Visitor' => $vendorDir . '/webonyx/graphql-php/src/Language/Visitor.php',
    'GraphQL\\Language\\VisitorOperation' => $vendorDir . '/webonyx/graphql-php/src/Language/VisitorOperation.php',
    'GraphQL\\Server\\Helper' => $vendorDir . '/webonyx/graphql-php/src/Server/Helper.php',
    'GraphQL\\Server\\OperationParams' => $vendorDir . '/webonyx/graphql-php/src/Server/OperationParams.php',
    'GraphQL\\Server\\RequestError' => $vendorDir . '/webonyx/graphql-php/src/Server/RequestError.php',
    'GraphQL\\Server\\ServerConfig' => $vendorDir . '/webonyx/graphql-php/src/Server/ServerConfig.php',
    'GraphQL\\Server\\StandardServer' => $vendorDir . '/webonyx/graphql-php/src/Server/StandardServer.php',
    'GraphQL\\Type\\Definition\\AbstractType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/AbstractType.php',
    'GraphQL\\Type\\Definition\\BooleanType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/BooleanType.php',
    'GraphQL\\Type\\Definition\\CompositeType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/CompositeType.php',
    'GraphQL\\Type\\Definition\\CustomScalarType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/CustomScalarType.php',
    'GraphQL\\Type\\Definition\\Directive' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/Directive.php',
    'GraphQL\\Type\\Definition\\EnumType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/EnumType.php',
    'GraphQL\\Type\\Definition\\EnumValueDefinition' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/EnumValueDefinition.php',
    'GraphQL\\Type\\Definition\\FieldArgument' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/FieldArgument.php',
    'GraphQL\\Type\\Definition\\FieldDefinition' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/FieldDefinition.php',
    'GraphQL\\Type\\Definition\\FloatType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/FloatType.php',
    'GraphQL\\Type\\Definition\\HasFieldsType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/HasFieldsType.php',
    'GraphQL\\Type\\Definition\\IDType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/IDType.php',
    'GraphQL\\Type\\Definition\\ImplementingType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/ImplementingType.php',
    'GraphQL\\Type\\Definition\\InputObjectField' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/InputObjectField.php',
    'GraphQL\\Type\\Definition\\InputObjectType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/InputObjectType.php',
    'GraphQL\\Type\\Definition\\InputType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/InputType.php',
    'GraphQL\\Type\\Definition\\IntType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/IntType.php',
    'GraphQL\\Type\\Definition\\InterfaceType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/InterfaceType.php',
    'GraphQL\\Type\\Definition\\LeafType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/LeafType.php',
    'GraphQL\\Type\\Definition\\ListOfType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/ListOfType.php',
    'GraphQL\\Type\\Definition\\NamedType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/NamedType.php',
    'GraphQL\\Type\\Definition\\NonNull' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/NonNull.php',
    'GraphQL\\Type\\Definition\\NullableType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/NullableType.php',
    'GraphQL\\Type\\Definition\\ObjectType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/ObjectType.php',
    'GraphQL\\Type\\Definition\\OutputType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/OutputType.php',
    'GraphQL\\Type\\Definition\\QueryPlan' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/QueryPlan.php',
    'GraphQL\\Type\\Definition\\ResolveInfo' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/ResolveInfo.php',
    'GraphQL\\Type\\Definition\\ScalarType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/ScalarType.php',
    'GraphQL\\Type\\Definition\\StringType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/StringType.php',
    'GraphQL\\Type\\Definition\\Type' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/Type.php',
    'GraphQL\\Type\\Definition\\TypeWithFields' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/TypeWithFields.php',
    'GraphQL\\Type\\Definition\\UnionType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/UnionType.php',
    'GraphQL\\Type\\Definition\\UnmodifiedType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/UnmodifiedType.php',
    'GraphQL\\Type\\Definition\\UnresolvedFieldDefinition' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/UnresolvedFieldDefinition.php',
    'GraphQL\\Type\\Definition\\WrappingType' => $vendorDir . '/webonyx/graphql-php/src/Type/Definition/WrappingType.php',
    'GraphQL\\Type\\Introspection' => $vendorDir . '/webonyx/graphql-php/src/Type/Introspection.php',
    'GraphQL\\Type\\Schema' => $vendorDir . '/webonyx/graphql-php/src/Type/Schema.php',
    'GraphQL\\Type\\SchemaConfig' => $vendorDir . '/webonyx/graphql-php/src/Type/SchemaConfig.php',
    'GraphQL\\Type\\SchemaValidationContext' => $vendorDir . '/webonyx/graphql-php/src/Type/SchemaValidationContext.php',
    'GraphQL\\Type\\TypeKind' => $vendorDir . '/webonyx/graphql-php/src/Type/TypeKind.php',
    'GraphQL\\Type\\Validation\\InputObjectCircularRefs' => $vendorDir . '/webonyx/graphql-php/src/Type/Validation/InputObjectCircularRefs.php',
    'GraphQL\\Utils\\AST' => $vendorDir . '/webonyx/graphql-php/src/Utils/AST.php',
    'GraphQL\\Utils\\ASTDefinitionBuilder' => $vendorDir . '/webonyx/graphql-php/src/Utils/ASTDefinitionBuilder.php',
    'GraphQL\\Utils\\BlockString' => $vendorDir . '/webonyx/graphql-php/src/Utils/BlockString.php',
    'GraphQL\\Utils\\BreakingChangesFinder' => $vendorDir . '/webonyx/graphql-php/src/Utils/BreakingChangesFinder.php',
    'GraphQL\\Utils\\BuildClientSchema' => $vendorDir . '/webonyx/graphql-php/src/Utils/BuildClientSchema.php',
    'GraphQL\\Utils\\BuildSchema' => $vendorDir . '/webonyx/graphql-php/src/Utils/BuildSchema.php',
    'GraphQL\\Utils\\InterfaceImplementations' => $vendorDir . '/webonyx/graphql-php/src/Utils/InterfaceImplementations.php',
    'GraphQL\\Utils\\MixedStore' => $vendorDir . '/webonyx/graphql-php/src/Utils/MixedStore.php',
    'GraphQL\\Utils\\PairSet' => $vendorDir . '/webonyx/graphql-php/src/Utils/PairSet.php',
    'GraphQL\\Utils\\SchemaExtender' => $vendorDir . '/webonyx/graphql-php/src/Utils/SchemaExtender.php',
    'GraphQL\\Utils\\SchemaPrinter' => $vendorDir . '/webonyx/graphql-php/src/Utils/SchemaPrinter.php',
    'GraphQL\\Utils\\TypeComparators' => $vendorDir . '/webonyx/graphql-php/src/Utils/TypeComparators.php',
    'GraphQL\\Utils\\TypeInfo' => $vendorDir . '/webonyx/graphql-php/src/Utils/TypeInfo.php',
    'GraphQL\\Utils\\Utils' => $vendorDir . '/webonyx/graphql-php/src/Utils/Utils.php',
    'GraphQL\\Utils\\Value' => $vendorDir . '/webonyx/graphql-php/src/Utils/Value.php',
    'GraphQL\\Validator\\ASTValidationContext' => $vendorDir . '/webonyx/graphql-php/src/Validator/ASTValidationContext.php',
    'GraphQL\\Validator\\DocumentValidator' => $vendorDir . '/webonyx/graphql-php/src/Validator/DocumentValidator.php',
    'GraphQL\\Validator\\Rules\\CustomValidationRule' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/CustomValidationRule.php',
    'GraphQL\\Validator\\Rules\\DisableIntrospection' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/DisableIntrospection.php',
    'GraphQL\\Validator\\Rules\\ExecutableDefinitions' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/ExecutableDefinitions.php',
    'GraphQL\\Validator\\Rules\\FieldsOnCorrectType' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/FieldsOnCorrectType.php',
    'GraphQL\\Validator\\Rules\\FragmentsOnCompositeTypes' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/FragmentsOnCompositeTypes.php',
    'GraphQL\\Validator\\Rules\\KnownArgumentNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/KnownArgumentNames.php',
    'GraphQL\\Validator\\Rules\\KnownArgumentNamesOnDirectives' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/KnownArgumentNamesOnDirectives.php',
    'GraphQL\\Validator\\Rules\\KnownDirectives' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/KnownDirectives.php',
    'GraphQL\\Validator\\Rules\\KnownFragmentNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/KnownFragmentNames.php',
    'GraphQL\\Validator\\Rules\\KnownTypeNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/KnownTypeNames.php',
    'GraphQL\\Validator\\Rules\\LoneAnonymousOperation' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/LoneAnonymousOperation.php',
    'GraphQL\\Validator\\Rules\\LoneSchemaDefinition' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/LoneSchemaDefinition.php',
    'GraphQL\\Validator\\Rules\\NoFragmentCycles' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/NoFragmentCycles.php',
    'GraphQL\\Validator\\Rules\\NoUndefinedVariables' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/NoUndefinedVariables.php',
    'GraphQL\\Validator\\Rules\\NoUnusedFragments' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/NoUnusedFragments.php',
    'GraphQL\\Validator\\Rules\\NoUnusedVariables' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/NoUnusedVariables.php',
    'GraphQL\\Validator\\Rules\\OverlappingFieldsCanBeMerged' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/OverlappingFieldsCanBeMerged.php',
    'GraphQL\\Validator\\Rules\\PossibleFragmentSpreads' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/PossibleFragmentSpreads.php',
    'GraphQL\\Validator\\Rules\\ProvidedRequiredArguments' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/ProvidedRequiredArguments.php',
    'GraphQL\\Validator\\Rules\\ProvidedRequiredArgumentsOnDirectives' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/ProvidedRequiredArgumentsOnDirectives.php',
    'GraphQL\\Validator\\Rules\\QueryComplexity' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/QueryComplexity.php',
    'GraphQL\\Validator\\Rules\\QueryDepth' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/QueryDepth.php',
    'GraphQL\\Validator\\Rules\\QuerySecurityRule' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/QuerySecurityRule.php',
    'GraphQL\\Validator\\Rules\\ScalarLeafs' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/ScalarLeafs.php',
    'GraphQL\\Validator\\Rules\\SingleFieldSubscription' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/SingleFieldSubscription.php',
    'GraphQL\\Validator\\Rules\\UniqueArgumentNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/UniqueArgumentNames.php',
    'GraphQL\\Validator\\Rules\\UniqueDirectivesPerLocation' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/UniqueDirectivesPerLocation.php',
    'GraphQL\\Validator\\Rules\\UniqueFragmentNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/UniqueFragmentNames.php',
    'GraphQL\\Validator\\Rules\\UniqueInputFieldNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/UniqueInputFieldNames.php',
    'GraphQL\\Validator\\Rules\\UniqueOperationNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/UniqueOperationNames.php',
    'GraphQL\\Validator\\Rules\\UniqueVariableNames' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/UniqueVariableNames.php',
    'GraphQL\\Validator\\Rules\\ValidationRule' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/ValidationRule.php',
    'GraphQL\\Validator\\Rules\\ValuesOfCorrectType' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/ValuesOfCorrectType.php',
    'GraphQL\\Validator\\Rules\\VariablesAreInputTypes' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/VariablesAreInputTypes.php',
    'GraphQL\\Validator\\Rules\\VariablesInAllowedPosition' => $vendorDir . '/webonyx/graphql-php/src/Validator/Rules/VariablesInAllowedPosition.php',
    'GraphQL\\Validator\\SDLValidationContext' => $vendorDir . '/webonyx/graphql-php/src/Validator/SDLValidationContext.php',
    'GraphQL\\Validator\\ValidationContext' => $vendorDir . '/webonyx/graphql-php/src/Validator/ValidationContext.php',
    'WPGraphQL\\Admin\\Admin' => $baseDir . '/src/Admin/Admin.php',
    'WPGraphQL\\Admin\\GraphiQL\\GraphiQL' => $baseDir . '/src/Admin/GraphiQL/GraphiQL.php',
    'WPGraphQL\\Admin\\Settings\\Settings' => $baseDir . '/src/Admin/Settings/Settings.php',
    'WPGraphQL\\Admin\\Settings\\SettingsRegistry' => $baseDir . '/src/Admin/Settings/SettingsRegistry.php',
    'WPGraphQL\\AppContext' => $baseDir . '/src/AppContext.php',
    'WPGraphQL\\Connection\\Comments' => $baseDir . '/src/Connection/Comments.php',
    'WPGraphQL\\Connection\\MenuItems' => $baseDir . '/src/Connection/MenuItems.php',
    'WPGraphQL\\Connection\\PostObjects' => $baseDir . '/src/Connection/PostObjects.php',
    'WPGraphQL\\Connection\\Revisions' => $baseDir . '/src/Connection/Revisions.php',
    'WPGraphQL\\Connection\\Taxonomies' => $baseDir . '/src/Connection/Taxonomies.php',
    'WPGraphQL\\Connection\\TermObjects' => $baseDir . '/src/Connection/TermObjects.php',
    'WPGraphQL\\Connection\\Users' => $baseDir . '/src/Connection/Users.php',
    'WPGraphQL\\Data\\CommentMutation' => $baseDir . '/src/Data/CommentMutation.php',
    'WPGraphQL\\Data\\Config' => $baseDir . '/src/Data/Config.php',
    'WPGraphQL\\Data\\Connection\\AbstractConnectionResolver' => $baseDir . '/src/Data/Connection/AbstractConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\CommentConnectionResolver' => $baseDir . '/src/Data/Connection/CommentConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\ContentTypeConnectionResolver' => $baseDir . '/src/Data/Connection/ContentTypeConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\EnqueuedScriptsConnectionResolver' => $baseDir . '/src/Data/Connection/EnqueuedScriptsConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\EnqueuedStylesheetConnectionResolver' => $baseDir . '/src/Data/Connection/EnqueuedStylesheetConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\MenuConnectionResolver' => $baseDir . '/src/Data/Connection/MenuConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\MenuItemConnectionResolver' => $baseDir . '/src/Data/Connection/MenuItemConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\PluginConnectionResolver' => $baseDir . '/src/Data/Connection/PluginConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\PostObjectConnectionResolver' => $baseDir . '/src/Data/Connection/PostObjectConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\TaxonomyConnectionResolver' => $baseDir . '/src/Data/Connection/TaxonomyConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\TermObjectConnectionResolver' => $baseDir . '/src/Data/Connection/TermObjectConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\ThemeConnectionResolver' => $baseDir . '/src/Data/Connection/ThemeConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\UserConnectionResolver' => $baseDir . '/src/Data/Connection/UserConnectionResolver.php',
    'WPGraphQL\\Data\\Connection\\UserRoleConnectionResolver' => $baseDir . '/src/Data/Connection/UserRoleConnectionResolver.php',
    'WPGraphQL\\Data\\Cursor\\CursorBuilder' => $baseDir . '/src/Data/Cursor/CursorBuilder.php',
    'WPGraphQL\\Data\\Cursor\\PostObjectCursor' => $baseDir . '/src/Data/Cursor/PostObjectCursor.php',
    'WPGraphQL\\Data\\Cursor\\TermObjectCursor' => $baseDir . '/src/Data/Cursor/TermObjectCursor.php',
    'WPGraphQL\\Data\\Cursor\\UserCursor' => $baseDir . '/src/Data/Cursor/UserCursor.php',
    'WPGraphQL\\Data\\DataSource' => $baseDir . '/src/Data/DataSource.php',
    'WPGraphQL\\Data\\Loader\\AbstractDataLoader' => $baseDir . '/src/Data/Loader/AbstractDataLoader.php',
    'WPGraphQL\\Data\\Loader\\CommentAuthorLoader' => $baseDir . '/src/Data/Loader/CommentAuthorLoader.php',
    'WPGraphQL\\Data\\Loader\\CommentLoader' => $baseDir . '/src/Data/Loader/CommentLoader.php',
    'WPGraphQL\\Data\\Loader\\EnqueuedScriptLoader' => $baseDir . '/src/Data/Loader/EnqueuedScriptLoader.php',
    'WPGraphQL\\Data\\Loader\\EnqueuedStylesheetLoader' => $baseDir . '/src/Data/Loader/EnqueuedStylesheetLoader.php',
    'WPGraphQL\\Data\\Loader\\PluginLoader' => $baseDir . '/src/Data/Loader/PluginLoader.php',
    'WPGraphQL\\Data\\Loader\\PostObjectLoader' => $baseDir . '/src/Data/Loader/PostObjectLoader.php',
    'WPGraphQL\\Data\\Loader\\PostTypeLoader' => $baseDir . '/src/Data/Loader/PostTypeLoader.php',
    'WPGraphQL\\Data\\Loader\\TaxonomyLoader' => $baseDir . '/src/Data/Loader/TaxonomyLoader.php',
    'WPGraphQL\\Data\\Loader\\TermObjectLoader' => $baseDir . '/src/Data/Loader/TermObjectLoader.php',
    'WPGraphQL\\Data\\Loader\\ThemeLoader' => $baseDir . '/src/Data/Loader/ThemeLoader.php',
    'WPGraphQL\\Data\\Loader\\UserLoader' => $baseDir . '/src/Data/Loader/UserLoader.php',
    'WPGraphQL\\Data\\Loader\\UserRoleLoader' => $baseDir . '/src/Data/Loader/UserRoleLoader.php',
    'WPGraphQL\\Data\\MediaItemMutation' => $baseDir . '/src/Data/MediaItemMutation.php',
    'WPGraphQL\\Data\\NodeResolver' => $baseDir . '/src/Data/NodeResolver.php',
    'WPGraphQL\\Data\\PostObjectMutation' => $baseDir . '/src/Data/PostObjectMutation.php',
    'WPGraphQL\\Data\\TermObjectMutation' => $baseDir . '/src/Data/TermObjectMutation.php',
    'WPGraphQL\\Data\\UserMutation' => $baseDir . '/src/Data/UserMutation.php',
    'WPGraphQL\\Model\\Avatar' => $baseDir . '/src/Model/Avatar.php',
    'WPGraphQL\\Model\\Comment' => $baseDir . '/src/Model/Comment.php',
    'WPGraphQL\\Model\\CommentAuthor' => $baseDir . '/src/Model/CommentAuthor.php',
    'WPGraphQL\\Model\\Menu' => $baseDir . '/src/Model/Menu.php',
    'WPGraphQL\\Model\\MenuItem' => $baseDir . '/src/Model/MenuItem.php',
    'WPGraphQL\\Model\\Model' => $baseDir . '/src/Model/Model.php',
    'WPGraphQL\\Model\\Plugin' => $baseDir . '/src/Model/Plugin.php',
    'WPGraphQL\\Model\\Post' => $baseDir . '/src/Model/Post.php',
    'WPGraphQL\\Model\\PostType' => $baseDir . '/src/Model/PostType.php',
    'WPGraphQL\\Model\\Taxonomy' => $baseDir . '/src/Model/Taxonomy.php',
    'WPGraphQL\\Model\\Term' => $baseDir . '/src/Model/Term.php',
    'WPGraphQL\\Model\\Theme' => $baseDir . '/src/Model/Theme.php',
    'WPGraphQL\\Model\\User' => $baseDir . '/src/Model/User.php',
    'WPGraphQL\\Model\\UserRole' => $baseDir . '/src/Model/UserRole.php',
    'WPGraphQL\\Mutation\\CommentCreate' => $baseDir . '/src/Mutation/CommentCreate.php',
    'WPGraphQL\\Mutation\\CommentDelete' => $baseDir . '/src/Mutation/CommentDelete.php',
    'WPGraphQL\\Mutation\\CommentRestore' => $baseDir . '/src/Mutation/CommentRestore.php',
    'WPGraphQL\\Mutation\\CommentUpdate' => $baseDir . '/src/Mutation/CommentUpdate.php',
    'WPGraphQL\\Mutation\\MediaItemCreate' => $baseDir . '/src/Mutation/MediaItemCreate.php',
    'WPGraphQL\\Mutation\\MediaItemDelete' => $baseDir . '/src/Mutation/MediaItemDelete.php',
    'WPGraphQL\\Mutation\\MediaItemUpdate' => $baseDir . '/src/Mutation/MediaItemUpdate.php',
    'WPGraphQL\\Mutation\\PostObjectCreate' => $baseDir . '/src/Mutation/PostObjectCreate.php',
    'WPGraphQL\\Mutation\\PostObjectDelete' => $baseDir . '/src/Mutation/PostObjectDelete.php',
    'WPGraphQL\\Mutation\\PostObjectUpdate' => $baseDir . '/src/Mutation/PostObjectUpdate.php',
    'WPGraphQL\\Mutation\\ResetUserPassword' => $baseDir . '/src/Mutation/ResetUserPassword.php',
    'WPGraphQL\\Mutation\\SendPasswordResetEmail' => $baseDir . '/src/Mutation/SendPasswordResetEmail.php',
    'WPGraphQL\\Mutation\\TermObjectCreate' => $baseDir . '/src/Mutation/TermObjectCreate.php',
    'WPGraphQL\\Mutation\\TermObjectDelete' => $baseDir . '/src/Mutation/TermObjectDelete.php',
    'WPGraphQL\\Mutation\\TermObjectUpdate' => $baseDir . '/src/Mutation/TermObjectUpdate.php',
    'WPGraphQL\\Mutation\\UpdateSettings' => $baseDir . '/src/Mutation/UpdateSettings.php',
    'WPGraphQL\\Mutation\\UserCreate' => $baseDir . '/src/Mutation/UserCreate.php',
    'WPGraphQL\\Mutation\\UserDelete' => $baseDir . '/src/Mutation/UserDelete.php',
    'WPGraphQL\\Mutation\\UserRegister' => $baseDir . '/src/Mutation/UserRegister.php',
    'WPGraphQL\\Mutation\\UserUpdate' => $baseDir . '/src/Mutation/UserUpdate.php',
    'WPGraphQL\\Registry\\SchemaRegistry' => $baseDir . '/src/Registry/SchemaRegistry.php',
    'WPGraphQL\\Registry\\TypeRegistry' => $baseDir . '/src/Registry/TypeRegistry.php',
    'WPGraphQL\\Request' => $baseDir . '/src/Request.php',
    'WPGraphQL\\Router' => $baseDir . '/src/Router.php',
    'WPGraphQL\\Server\\ValidationRules\\DisableIntrospection' => $baseDir . '/src/Server/ValidationRules/DisableIntrospection.php',
    'WPGraphQL\\Server\\ValidationRules\\QueryDepth' => $baseDir . '/src/Server/ValidationRules/QueryDepth.php',
    'WPGraphQL\\Server\\ValidationRules\\RequireAuthentication' => $baseDir . '/src/Server/ValidationRules/RequireAuthentication.php',
    'WPGraphQL\\Server\\WPHelper' => $baseDir . '/src/Server/WPHelper.php',
    'WPGraphQL\\Type\\Enum\\AvatarRatingEnum' => $baseDir . '/src/Type/Enum/AvatarRatingEnum.php',
    'WPGraphQL\\Type\\Enum\\CommentsConnectionOrderbyEnum' => $baseDir . '/src/Type/Enum/CommentsConnectionOrderbyEnum.php',
    'WPGraphQL\\Type\\Enum\\ContentNodeIdTypeEnum' => $baseDir . '/src/Type/Enum/ContentNodeIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\ContentTypeEnum' => $baseDir . '/src/Type/Enum/ContentTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\ContentTypeIdTypeEnum' => $baseDir . '/src/Type/Enum/ContentTypeIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\MediaItemSizeEnum' => $baseDir . '/src/Type/Enum/MediaItemSizeEnum.php',
    'WPGraphQL\\Type\\Enum\\MediaItemStatusEnum' => $baseDir . '/src/Type/Enum/MediaItemStatusEnum.php',
    'WPGraphQL\\Type\\Enum\\MenuItemNodeIdTypeEnum' => $baseDir . '/src/Type/Enum/MenuItemNodeIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\MenuLocationEnum' => $baseDir . '/src/Type/Enum/MenuLocationEnum.php',
    'WPGraphQL\\Type\\Enum\\MenuNodeIdTypeEnum' => $baseDir . '/src/Type/Enum/MenuNodeIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\MimeTypeEnum' => $baseDir . '/src/Type/Enum/MimeTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\OrderEnum' => $baseDir . '/src/Type/Enum/OrderEnum.php',
    'WPGraphQL\\Type\\Enum\\PostObjectFieldFormatEnum' => $baseDir . '/src/Type/Enum/PostObjectFieldFormatEnum.php',
    'WPGraphQL\\Type\\Enum\\PostObjectsConnectionDateColumnEnum' => $baseDir . '/src/Type/Enum/PostObjectsConnectionDateColumnEnum.php',
    'WPGraphQL\\Type\\Enum\\PostObjectsConnectionOrderbyEnum' => $baseDir . '/src/Type/Enum/PostObjectsConnectionOrderbyEnum.php',
    'WPGraphQL\\Type\\Enum\\PostStatusEnum' => $baseDir . '/src/Type/Enum/PostStatusEnum.php',
    'WPGraphQL\\Type\\Enum\\RelationEnum' => $baseDir . '/src/Type/Enum/RelationEnum.php',
    'WPGraphQL\\Type\\Enum\\TaxonomyEnum' => $baseDir . '/src/Type/Enum/TaxonomyEnum.php',
    'WPGraphQL\\Type\\Enum\\TaxonomyIdTypeEnum' => $baseDir . '/src/Type/Enum/TaxonomyIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\TermNodeIdTypeEnum' => $baseDir . '/src/Type/Enum/TermNodeIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\TermObjectsConnectionOrderbyEnum' => $baseDir . '/src/Type/Enum/TermObjectsConnectionOrderbyEnum.php',
    'WPGraphQL\\Type\\Enum\\TimezoneEnum' => $baseDir . '/src/Type/Enum/TimezoneEnum.php',
    'WPGraphQL\\Type\\Enum\\UserNodeIdTypeEnum' => $baseDir . '/src/Type/Enum/UserNodeIdTypeEnum.php',
    'WPGraphQL\\Type\\Enum\\UserRoleEnum' => $baseDir . '/src/Type/Enum/UserRoleEnum.php',
    'WPGraphQL\\Type\\Enum\\UsersConnectionOrderbyEnum' => $baseDir . '/src/Type/Enum/UsersConnectionOrderbyEnum.php',
    'WPGraphQL\\Type\\Enum\\UsersConnectionSearchColumnEnum' => $baseDir . '/src/Type/Enum/UsersConnectionSearchColumnEnum.php',
    'WPGraphQL\\Type\\Input\\DateInput' => $baseDir . '/src/Type/Input/DateInput.php',
    'WPGraphQL\\Type\\Input\\DateQueryInput' => $baseDir . '/src/Type/Input/DateQueryInput.php',
    'WPGraphQL\\Type\\Input\\MenuItemsConnectionWhereArgs' => $baseDir . '/src/Type/Input/MenuItemsConnectionWhereArgs.php',
    'WPGraphQL\\Type\\Input\\PostObjectsConnectionOrderbyInput' => $baseDir . '/src/Type/Input/PostObjectsConnectionOrderbyInput.php',
    'WPGraphQL\\Type\\Input\\UsersConnectionOrderbyInput' => $baseDir . '/src/Type/Input/UsersConnectionOrderbyInput.php',
    'WPGraphQL\\Type\\InterfaceType\\CommenterInterface' => $baseDir . '/src/Type/InterfaceType/CommenterInterface.php',
    'WPGraphQL\\Type\\InterfaceType\\ContentNode' => $baseDir . '/src/Type/InterfaceType/ContentNode.php',
    'WPGraphQL\\Type\\InterfaceType\\ContentTemplate' => $baseDir . '/src/Type/InterfaceType/ContentTemplate.php',
    'WPGraphQL\\Type\\InterfaceType\\DatabaseIdentifier' => $baseDir . '/src/Type/InterfaceType/DatabaseIdentifier.php',
    'WPGraphQL\\Type\\InterfaceType\\EnqueuedAsset' => $baseDir . '/src/Type/InterfaceType/EnqueuedAsset.php',
    'WPGraphQL\\Type\\InterfaceType\\HierarchicalContentNode' => $baseDir . '/src/Type/InterfaceType/HierarchicalContentNode.php',
    'WPGraphQL\\Type\\InterfaceType\\HierarchicalTermNode' => $baseDir . '/src/Type/InterfaceType/HierarchicalTermNode.php',
    'WPGraphQL\\Type\\InterfaceType\\MenuItemLinkable' => $baseDir . '/src/Type/InterfaceType/MenuItemLinkable.php',
    'WPGraphQL\\Type\\InterfaceType\\Node' => $baseDir . '/src/Type/InterfaceType/Node.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithAuthor' => $baseDir . '/src/Type/InterfaceType/NodeWithAuthor.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithComments' => $baseDir . '/src/Type/InterfaceType/NodeWithComments.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithContentEditor' => $baseDir . '/src/Type/InterfaceType/NodeWithContentEditor.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithExcerpt' => $baseDir . '/src/Type/InterfaceType/NodeWithExcerpt.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithFeaturedImage' => $baseDir . '/src/Type/InterfaceType/NodeWithFeaturedImage.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithPageAttributes' => $baseDir . '/src/Type/InterfaceType/NodeWithPageAttributes.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithRevisions' => $baseDir . '/src/Type/InterfaceType/NodeWithRevisions.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithTemplate' => $baseDir . '/src/Type/InterfaceType/NodeWithTemplate.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithTitle' => $baseDir . '/src/Type/InterfaceType/NodeWithTitle.php',
    'WPGraphQL\\Type\\InterfaceType\\NodeWithTrackbacks' => $baseDir . '/src/Type/InterfaceType/NodeWithTrackbacks.php',
    'WPGraphQL\\Type\\InterfaceType\\TermNode' => $baseDir . '/src/Type/InterfaceType/TermNode.php',
    'WPGraphQL\\Type\\InterfaceType\\UniformResourceIdentifiable' => $baseDir . '/src/Type/InterfaceType/UniformResourceIdentifiable.php',
    'WPGraphQL\\Type\\ObjectType\\Avatar' => $baseDir . '/src/Type/ObjectType/Avatar.php',
    'WPGraphQL\\Type\\ObjectType\\Comment' => $baseDir . '/src/Type/ObjectType/Comment.php',
    'WPGraphQL\\Type\\ObjectType\\CommentAuthor' => $baseDir . '/src/Type/ObjectType/CommentAuthor.php',
    'WPGraphQL\\Type\\ObjectType\\ContentType' => $baseDir . '/src/Type/ObjectType/ContentType.php',
    'WPGraphQL\\Type\\ObjectType\\EnqueuedScript' => $baseDir . '/src/Type/ObjectType/EnqueuedScript.php',
    'WPGraphQL\\Type\\ObjectType\\EnqueuedStylesheet' => $baseDir . '/src/Type/ObjectType/EnqueuedStylesheet.php',
    'WPGraphQL\\Type\\ObjectType\\MediaDetails' => $baseDir . '/src/Type/ObjectType/MediaDetails.php',
    'WPGraphQL\\Type\\ObjectType\\MediaItemMeta' => $baseDir . '/src/Type/ObjectType/MediaItemMeta.php',
    'WPGraphQL\\Type\\ObjectType\\MediaSize' => $baseDir . '/src/Type/ObjectType/MediaSize.php',
    'WPGraphQL\\Type\\ObjectType\\Menu' => $baseDir . '/src/Type/ObjectType/Menu.php',
    'WPGraphQL\\Type\\ObjectType\\MenuItem' => $baseDir . '/src/Type/ObjectType/MenuItem.php',
    'WPGraphQL\\Type\\ObjectType\\PageInfo' => $baseDir . '/src/Type/ObjectType/PageInfo.php',
    'WPGraphQL\\Type\\ObjectType\\Plugin' => $baseDir . '/src/Type/ObjectType/Plugin.php',
    'WPGraphQL\\Type\\ObjectType\\PostObject' => $baseDir . '/src/Type/ObjectType/PostObject.php',
    'WPGraphQL\\Type\\ObjectType\\PostTypeLabelDetails' => $baseDir . '/src/Type/ObjectType/PostTypeLabelDetails.php',
    'WPGraphQL\\Type\\ObjectType\\RootMutation' => $baseDir . '/src/Type/ObjectType/RootMutation.php',
    'WPGraphQL\\Type\\ObjectType\\RootQuery' => $baseDir . '/src/Type/ObjectType/RootQuery.php',
    'WPGraphQL\\Type\\ObjectType\\SettingGroup' => $baseDir . '/src/Type/ObjectType/SettingGroup.php',
    'WPGraphQL\\Type\\ObjectType\\Settings' => $baseDir . '/src/Type/ObjectType/Settings.php',
    'WPGraphQL\\Type\\ObjectType\\Taxonomy' => $baseDir . '/src/Type/ObjectType/Taxonomy.php',
    'WPGraphQL\\Type\\ObjectType\\TermObject' => $baseDir . '/src/Type/ObjectType/TermObject.php',
    'WPGraphQL\\Type\\ObjectType\\Theme' => $baseDir . '/src/Type/ObjectType/Theme.php',
    'WPGraphQL\\Type\\ObjectType\\User' => $baseDir . '/src/Type/ObjectType/User.php',
    'WPGraphQL\\Type\\ObjectType\\UserRole' => $baseDir . '/src/Type/ObjectType/UserRole.php',
    'WPGraphQL\\Type\\Union\\ContentRevisionUnion' => $baseDir . '/src/Type/Union/ContentRevisionUnion.php',
    'WPGraphQL\\Type\\Union\\MenuItemObjectUnion' => $baseDir . '/src/Type/Union/MenuItemObjectUnion.php',
    'WPGraphQL\\Type\\Union\\PostObjectUnion' => $baseDir . '/src/Type/Union/PostObjectUnion.php',
    'WPGraphQL\\Type\\Union\\TermObjectUnion' => $baseDir . '/src/Type/Union/TermObjectUnion.php',
    'WPGraphQL\\Type\\WPConnectionType' => $baseDir . '/src/Type/WPConnectionType.php',
    'WPGraphQL\\Type\\WPEnumType' => $baseDir . '/src/Type/WPEnumType.php',
    'WPGraphQL\\Type\\WPInputObjectType' => $baseDir . '/src/Type/WPInputObjectType.php',
    'WPGraphQL\\Type\\WPInterfaceTrait' => $baseDir . '/src/Type/WPInterfaceTrait.php',
    'WPGraphQL\\Type\\WPInterfaceType' => $baseDir . '/src/Type/WPInterfaceType.php',
    'WPGraphQL\\Type\\WPObjectType' => $baseDir . '/src/Type/WPObjectType.php',
    'WPGraphQL\\Type\\WPScalar' => $baseDir . '/src/Type/WPScalar.php',
    'WPGraphQL\\Type\\WPUnionType' => $baseDir . '/src/Type/WPUnionType.php',
    'WPGraphQL\\Types' => $baseDir . '/src/Types.php',
    'WPGraphQL\\Utils\\DebugLog' => $baseDir . '/src/Utils/DebugLog.php',
    'WPGraphQL\\Utils\\InstrumentSchema' => $baseDir . '/src/Utils/InstrumentSchema.php',
    'WPGraphQL\\Utils\\Preview' => $baseDir . '/src/Utils/Preview.php',
    'WPGraphQL\\Utils\\QueryLog' => $baseDir . '/src/Utils/QueryLog.php',
    'WPGraphQL\\Utils\\Tracing' => $baseDir . '/src/Utils/Tracing.php',
    'WPGraphQL\\Utils\\Utils' => $baseDir . '/src/Utils/Utils.php',
    'WPGraphQL\\WPSchema' => $baseDir . '/src/WPSchema.php',
);
