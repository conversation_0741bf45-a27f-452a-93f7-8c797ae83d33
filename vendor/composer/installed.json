{"packages": [{"name": "ivome/graphql-relay-php", "version": "v0.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ivome/graphql-relay-php.git", "reference": "7055fd45b7e552cee4d1290849b84294b0049373"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ivome/graphql-relay-php/zipball/7055fd45b7e552cee4d1290849b84294b0049373", "reference": "7055fd45b7e552cee4d1290849b84294b0049373", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "webonyx/graphql-php": "^14.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "satooshi/php-coveralls": "~1.0"}, "time": "2021-04-24T19:31:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A PHP port of GraphQL Relay reference implementation", "homepage": "https://github.com/ivome/graphql-relay-php", "keywords": ["<PERSON><PERSON>", "api", "graphql"], "support": {"issues": "https://github.com/ivome/graphql-relay-php/issues", "source": "https://github.com/ivome/graphql-relay-php/tree/v0.6.0"}, "install-path": "../ivome/graphql-relay-php"}, {"name": "webonyx/graphql-php", "version": "v14.9.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/webonyx/graphql-php.git", "reference": "36b83621deb5eae354347a2e86dc7aec81b32a82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/36b83621deb5eae354347a2e86dc7aec81b32a82", "reference": "36b83621deb5eae354347a2e86dc7aec81b32a82", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1||^8.0"}, "require-dev": {"amphp/amp": "^2.3", "doctrine/coding-standard": "^6.0", "nyholm/psr7": "^1.2", "phpbench/phpbench": "^0.16.10", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "0.12.82", "phpstan/phpstan-phpunit": "0.12.18", "phpstan/phpstan-strict-rules": "0.12.9", "phpunit/phpunit": "^7.2|^8.5", "psr/http-message": "^1.0", "react/promise": "2.*", "simpod/php-coveralls-mirror": "^3.0", "squizlabs/php_codesniffer": "3.5.4"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "time": "2021-06-15T16:14:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "support": {"issues": "https://github.com/webonyx/graphql-php/issues", "source": "https://github.com/webonyx/graphql-php/tree/v14.9.0"}, "funding": [{"url": "https://opencollective.com/webonyx-graphql-php", "type": "open_collective"}], "install-path": "../webonyx/graphql-php"}], "dev": false, "dev-package-names": []}