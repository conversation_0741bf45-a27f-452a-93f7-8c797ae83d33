<?php return array(
    'root' => array(
        'name' => 'wp-graphql/wp-graphql-jwt-authentication',
        'pretty_version' => 'dev-develop',
        'version' => 'dev-develop',
        'reference' => '3dc37ab377471719b07ff26571a8dee1d1c0943e',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'reference' => '4dd1e007f22a927ac77da5a3fbb067b42d3bc224',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wp-graphql/wp-graphql-jwt-authentication' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'reference' => '3dc37ab377471719b07ff26571a8dee1d1c0943e',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
