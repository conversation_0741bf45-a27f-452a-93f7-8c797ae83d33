# Codeception Test Suite Configuration
#
# Suite for unit or integration tests that require WordPress functions and classes.
actor: WpunitTester
modules:
  enabled:
    - WPLoader
  disabled:
    - WPDb
    - WPBrowser
  config:
    WPDb:
      cleanup: false
    WPLoader:
      plugins:
        - wp-graphql/wp-graphql.php
        - wp-graphql-jwt-authentication/wp-graphql-jwt-authentication.php
      activatePlugins:
        - wp-graphql/wp-graphql.php
        - wp-graphql-jwt-authentication/wp-graphql-jwt-authentication.php
      configFile:
        - 'tests/_data/config.php'
