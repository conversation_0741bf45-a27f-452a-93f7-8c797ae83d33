!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("podcastIframe",[],t):"object"==typeof exports?exports.podcastIframe=t():e.podcastIframe=t()}(window,(function(){return function(e){var t={};function o(i){if(t[i])return t[i].exports;var n=t[i]={i:i,l:!1,exports:{}};return e[i].call(n.exports,n,n.exports,o),n.l=!0,n.exports}return o.m=e,o.c=t,o.d=function(e,t,i){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(o.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(i,n,function(t){return e[t]}.bind(null,n));return i},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=0)}([function(e,t,o){"use strict";function i(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}o.r(t);const n=console.log,r="PODCAST",s={green:"color: #018a01",red:"color: #ff1e1e",orange:"color: #f98a00"};var d=class{constructor(e){this.enabled=e,i(this,"log",e=>{this.enabled&&n(`[${r}] ${e}`)}),i(this,"success",e=>{this.enabled&&n(`%c[${r}] ${e}`,s.green)}),i(this,"info",e=>{this.enabled&&n(`%c[${r}] ${e}`,s.orange)}),i(this,"warn",e=>{this.enabled&&n(`%c[${r}] ${e}`,s.red)})}};const u=e=>/^(true|1)$/i.test(e);function l(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const a=["elementId","podcastId","key"];var c=class{constructor(e){this.moduleConfig=e,l(this,"debug",void 0),l(this,"elementId",void 0),l(this,"podcastId",void 0),l(this,"key",void 0),l(this,"podcastUrl","https://www.rtvs.sk/embed/podcast/"),l(this,"elementHolder",void 0),l(this,"iframeEl",void 0),l(this,"checkProps",()=>{a.map(e=>{this.moduleConfig.hasOwnProperty(e)||this.debug.warn(`'${e}' is required`)}),this.podcastId=this.moduleConfig.podcastId,this.key=this.moduleConfig.key,"string"==typeof this.key||"number"==typeof this.key?null!==document.getElementById(this.moduleConfig.elementId)?"string"==typeof this.podcastId||"number"==typeof this.podcastId?(this.debug.info(`podcast url ${this.podcastUrl+this.podcastId}?key=${this.key}`),this.debug.success(`inserting iframe in element #${this.moduleConfig.elementId}`),this.elementHolder=document.getElementById(this.moduleConfig.elementId),this.insertIframe()):this.debug.warn("value of 'podcastId' must be a type of string or number"):this.debug.warn(`${this.moduleConfig.elementId?"element #"+this.moduleConfig.elementId+" not found":"'elementId' not defined"}`):this.debug.warn("value of 'key' must be a type of string or number")}),l(this,"insertIframe",()=>{this.iframeEl=document.createElement("iframe"),this.iframeEl.setAttribute("id",`podcast_${this.elementId}`),this.iframeEl.setAttribute("width","100%"),this.iframeEl.setAttribute("height","100%"),this.iframeEl.setAttribute("src",`${this.podcastUrl+this.podcastId}?key=${this.key}`),this.elementHolder.appendChild(this.iframeEl)}),this.debug=new d(!!u(this.moduleConfig.debug)&&this.moduleConfig.debug),this.debug.log("logging into console is allowed"),this.checkProps()}};function f(e){return new c(e)}o.d(t,"init",(function(){return f})),document.addEventListener("DOMContentLoaded",()=>{(e=>document.querySelectorAll(`[${e}]`))("data-podcast-id").forEach((e,t)=>{let o=e.getAttribute("data-podcast-debug"),i=e.getAttribute("data-podcast-id");e.setAttribute("id",`podcast-${i}-${t}`);let n=e.getAttribute("id"),r=e.getAttribute("data-podcast-key");return new c({debug:o,elementId:n,podcastId:i,key:r})})})}])}));