!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("videoIframe",[],t):"object"==typeof exports?exports.videoIframe=t():e.videoIframe=t()}(window,(function(){return function(e){var t={};function i(o){if(t[o])return t[o].exports;var n=t[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,i),n.l=!0,n.exports}return i.m=e,i.c=t,i.d=function(e,t,o){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(i.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(o,n,function(t){return e[t]}.bind(null,n));return o},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=0)}([function(e,t,i){"use strict";function o(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}i.r(t);const n=console.log,r="RTVSVIDEO",d={green:"color: #018a01",red:"color: #ff1e1e",orange:"color: #f98a00"};var s=class{constructor(e){this.enabled=e,o(this,"log",e=>{this.enabled&&n(`[${r}] ${e}`)}),o(this,"success",e=>{this.enabled&&n(`%c[${r}] ${e}`,d.green)}),o(this,"info",e=>{this.enabled&&n(`%c[${r}] ${e}`,d.orange)}),o(this,"warn",e=>{this.enabled&&n(`%c[${r}] ${e}`,d.red)})}};const u=e=>/^(true|1)$/i.test(e);function l(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}const f=["elementId","videoId","key"];var a=class{constructor(e){this.moduleConfig=e,l(this,"debug",void 0),l(this,"elementId",void 0),l(this,"videoId",void 0),l(this,"key",void 0),l(this,"videoUrl","https://www.rtvs.sk/embed/video/"),l(this,"elementHolder",void 0),l(this,"iframeEl",void 0),l(this,"checkProps",()=>{f.map(e=>{this.moduleConfig.hasOwnProperty(e)||this.debug.warn(`'${e}' is required`)}),this.videoId=this.moduleConfig.videoId,this.key=this.moduleConfig.key,"string"==typeof this.key||"number"==typeof this.key?null!==document.getElementById(this.moduleConfig.elementId)?"string"==typeof this.videoId||"number"==typeof this.videoId?(this.debug.info(`video url ${this.videoUrl+this.videoId}?key=${this.key}`),this.debug.success(`inserting iframe in element #${this.moduleConfig.elementId}`),this.elementHolder=document.getElementById(this.moduleConfig.elementId),this.insertIframe()):this.debug.warn("value of 'videoId' must be a type of string or number"):this.debug.warn(`${this.moduleConfig.elementId?"element #"+this.moduleConfig.elementId+" not found":"'elementId' not defined"}`):this.debug.warn("value of 'key' must be a type of string or number")}),l(this,"insertIframe",()=>{this.iframeEl=document.createElement("iframe"),this.iframeEl.setAttribute("id",`video_${this.elementId}`),this.iframeEl.setAttribute("width","100%"),this.iframeEl.setAttribute("height","100%"),this.iframeEl.setAttribute("src",`${this.videoUrl+this.videoId}?key=${this.key}`),this.elementHolder.appendChild(this.iframeEl)}),this.debug=new s(!!u(this.moduleConfig.debug)&&this.moduleConfig.debug),this.debug.log("logging into console is allowed"),this.checkProps()}};function c(e){return new a(e)}i.d(t,"init",(function(){return c})),document.addEventListener("DOMContentLoaded",()=>{(e=>document.querySelectorAll(`[${e}]`))("data-video-id").forEach((e,t)=>{let i=e.getAttribute("data-video-debug"),o=e.getAttribute("data-video-id");e.setAttribute("id",`video-${o}-${t}`);let n=e.getAttribute("id"),r=e.getAttribute("data-video-key");return new a({debug:i,elementId:n,videoId:o,key:r})})})}])}));