<?php



	
define("WP_ROOT", __DIR__);
define("DS", DIRECTORY_SEPARATOR);
require_once WP_ROOT . DS . "../wp-blog-header.php";
require_once(WP_ROOT . DS . '../wp-admin/includes/media.php');
require_once(WP_ROOT . DS . '../wp-admin/includes/file.php');
require_once(WP_ROOT . DS . '../wp-admin/includes/image.php');

/**
 * check if program exists in db
 */

global $wpdb;
$db_programs = $wpdb->get_results("SELECT * from tv_archive");

foreach($db_programs as $db_program){
    $program_obj = json_decode($db_program->body, true);

    if(!has_post_thumbnail($db_program->wp_id)){
        $image_url = $program_obj['video']['image'];

        if(!$image_url){
            $image_url = $program_obj['series']['image'];
        }

        if($image_url){
            if($image_url!='https://static.rtvs.sk/media...'){
                echo $image_url;
                $att_id = media_sideload_image( $image_url, $db_program->wp_id, $program_obj['name'],'id');
                echo $att_id;
                set_post_thumbnail( $db_program->wp_id, $att_id );
            }
        }
    }


    wp_set_post_tags( $db_program->wp_id, ['video',$program_obj['series']['name']], true );

    $content = "";

	if($program_obj['description']){
		$content .= '<!-- wp:paragraph -->
    '.$program_obj['description'].'
<!-- /wp:paragraph -->';
	}elseif($program_obj['synopsis']){
        $content .= '<!-- wp:paragraph -->
<p>'.$program_obj['synopsis'].'</p>
<!-- /wp:paragraph -->'."\n";
    }

    $content .= '<!-- wp:html -->
<div class="video-wrapper-outer">
	<iframe src="https://www.rtvs.sk/embed/video/'.$program_obj['id'].'?key=ompCy7mew0Q1ZYUh0DBn-5aSH" allowfullscreen></iframe>
    <div class="video-wrapper-ratio"></div>
</div>
<!-- /wp:html -->'."\n";

    kses_remove_filters(); 
	$res = wp_update_post( array(
		'ID' 				=> $db_program->wp_id,
		'post_title'		=> $program_obj['name'],
		'post_excerpt'		=> $program_obj['synopsis'],
        'post_content'		=> $content,
        'post_date_gmt'     => get_gmt_from_date( $program_obj['airdatetime'] ),
        'post_date'         => $program_obj['airdatetime']
    ), true);
    kses_init_filters();
    echo get_gmt_from_date( $program_obj['airdatetime'] )."\n";
}



