<?php

/**
 * Plugin Name: Creanet Minúta po minúte
 * Description:
 * Version: 1.1.0
 * Author: <PERSON>
 * Author URI: https://creanet.sk/
 * Text Domain: creanet-minuta-po-minute
 * Domain Path: /languages
 */

use Creanet\MinutaPoMinute\Init;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/*
 * |--------------------------------------------------------------------------
 * | ANCHOR Constants
 * |--------------------------------------------------------------------------
 */

if ( ! defined( 'CREANET_MINUTA_PO_MINUTE_PLUGIN_FILE' ) ) {
	define( 'CREANET_MINUTA_PO_MINUTE_PLUGIN_FILE', __FILE__ );
}

if ( ! defined( 'CREANET_MINUTA_PO_MINUTE_PLUGIN_DIR' ) ) {
	define( 'CREANET_MINUTA_PO_MINUTE_PLUGIN_DIR', __DIR__ . '/' );
}

if ( ! defined( 'CREANET_MINUTA_PO_MINUTE_PLUGIN_URL' ) ) {
	define( 'CREANET_MINUTA_PO_MINUTE_PLUGIN_URL', plugin_dir_url( CREANET_MINUTA_PO_MINUTE_PLUGIN_FILE ) );
}

/*
 * |--------------------------------------------------------------------------
 * | ANCHOR Version
 * |--------------------------------------------------------------------------
 */

/**
 * Returns the current plugin version if available or `null` otherwise
 */
function creanet_minuta_po_minute_plugin_version() {
	return '1.0.10';
}


/*
|--------------------------------------------------------------------------
| ANCHOR Require files
|--------------------------------------------------------------------------
*/
// if is single post

// Enqueue scripts
add_action(
	'wp_enqueue_scripts',
	function () {
		wp_enqueue_script(
			'creanet-minuta-po-minute',
			CREANET_MINUTA_PO_MINUTE_PLUGIN_URL . 'dist/bundlem.js',
			array(),
			creanet_minuta_po_minute_plugin_version(),
			true
		);
	},
	100
);

// Enqueue scripts for editor
add_action(
	'enqueue_block_editor_assets',
	function () {
		wp_enqueue_script(
			'creanet-minuta-po-minute-editor',
			CREANET_MINUTA_PO_MINUTE_PLUGIN_URL . 'dist/bundlem.js',
			array(),
			creanet_minuta_po_minute_plugin_version(),
			true
		);
	},
	100
);

// Enqueue styles
add_action(
	'wp_enqueue_scripts',
	function () {
		wp_enqueue_style(
			'creanet-minuta-po-minute',
			CREANET_MINUTA_PO_MINUTE_PLUGIN_URL . 'dist/bundlem.css',
			array(),
			creanet_minuta_po_minute_plugin_version()
		);
	},
	100
);

// Enqueue styles for editor
add_action(
	'enqueue_block_editor_assets',
	function () {
		wp_enqueue_style(
			'creanet-minuta-po-minute-editor',
			CREANET_MINUTA_PO_MINUTE_PLUGIN_URL . 'dist/bundlem.css',
			array(),
			creanet_minuta_po_minute_plugin_version()
		);
	},
	100
);

require_once CREANET_MINUTA_PO_MINUTE_PLUGIN_DIR . 'vendor/autoload.php';

/*
 * |--------------------------------------------------------------------------
 * | ANCHOR Init
 * |--------------------------------------------------------------------------
 */

add_action(
	'plugins_loaded',
	function () {
		$init = new Init();
		$init->run();
	}
);
