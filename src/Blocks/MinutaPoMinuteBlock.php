<?php

namespace Creanet\MinutaPoMinute\Blocks;

use Creanet\MinutaPoMinute\Core\Block;

class MinutaPoMinuteBlock extends Block {

	public function getBlockTitle(): string {
		return 'Minuta po minúte';
	}

	public function getFieldsArray(): array {
		return [
			[
				'key'        => 'field_' . $this->getBlockName() . '_category',
				'label'      => __( 'Kategória' ),
				'name'       => 'category',
				'type'       => 'taxonomy',
				'taxonomy'   => 'minuta-po-minute-category',
				'field_type' => 'select',
				'allow_null' => 0,
			],
			[
				'key'           => 'field_' . $this->getBlockName() . '_per_page',
				'label'         => __( 'Počet zobrazených článkov na jedno načítanie' ),
				'name'          => 'per_page',
				'type'          => 'number',
				'step'          => 1,
				'min'           => 1,
				'max'           => 100,
				'default_value' => 10,
			]
		];
	}

	public function getBlockName(): string {
		return 'minuta-po-minute';
	}

	public function render( $block, $content = '', $is_preview = false, $post_id = 0 ): void {
		$context = [
			'block'      => $block,
			'content'    => $content,
			'is_preview' => $is_preview,
			'post_id'    => $post_id,
		];

		if ( ! $context ) {
			return;
		}

		if ( ! array_key_exists( 'data', $context['block'] ) ) {
			return;
		}

		if ( ! array_key_exists( 'category', $context['block']['data'] ) ) {
			return;
		}

		if ( ! array_key_exists( 'per_page', $context['block']['data'] ) ) {
			return;
		}

		?>
      <div id="minuta-po-minute"
           data-category='<?php echo $context['block']['data']['category'] ?>'
           data-per-page='<?php echo $context['block']['data']['per_page'] ?>'
      ></div>
		<?php
	}
}
