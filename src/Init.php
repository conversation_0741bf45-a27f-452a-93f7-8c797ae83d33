<?php

namespace Creanet\MinutaPoMinute;

class Init {

	/**
	 * Runs the action.
	 *
	 * This method executes the necessary action by registering hooks.
	 *
	 * @return void
	 */
	public function run(): void {
		$this->register_hooks();
	}

	/**
	 * Registers hooks for the action.
	 *
	 * This method registers hooks for the action by adding action callbacks to the 'init' hook.
	 * The action callbacks are methods of this class: 'register_post_type', 'register_taxonomy', 'register_post_status'.
	 *
	 * @return void
	 */
	private function register_hooks(): void {
		add_action( 'init', [ $this, 'register_post_type' ] );
		add_action( 'init', [ $this, 'register_taxonomy' ] );
		add_action( 'init', [ $this, 'register_block' ] );
	}

	public function register_block(): void {
		( new Blocks\MinutaPoMinuteBlock() )->register();
	}

	/**
	 * Registers the 'minuta-po-minute' post type.
	 *
	 * @return void
	 */
	public function register_post_type(): void {
		( new Posts\MinutaPoMinutePost() )->register();
	}

	/**
	 * Registers the 'minuta-po-minute-category' taxonomy.
	 *
	 * @return void
	 */
	public function register_taxonomy(): void {
		( new Taxonomies\MinutaPoMinuteCategory() )->register();
	}
}
