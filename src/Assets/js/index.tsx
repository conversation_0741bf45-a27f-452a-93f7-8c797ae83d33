import React from 'react';
import { createRoot } from 'react-dom/client';
import MinutaPoMinute from './MinutaPoMinute';

/**
 * The entrypoint for the application.
 */
const main = () => {
  // React needs to be available globally.
  window.React = window.React || React;

  // Initialize the React components.
  window.requestAnimationFrame(() => {
    const root = document.getElementById('minuta-po-minute');

    const category = root?.dataset.category ?? '';
    let perPage = parseInt(root?.dataset.perPage ?? '10', 10) ?? 10;

    if (root) {
      createRoot(root).render(<MinutaPoMinute category={category} perPage={perPage} />);
    }
  });
};

main();
