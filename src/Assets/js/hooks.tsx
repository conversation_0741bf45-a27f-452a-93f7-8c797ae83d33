import React, { useEffect } from 'react';

/**
 * Fetches data from the specified URL.
 *
 * @param {string} url - The URL to fetch.
 * @param loadMore
 * @param refetch
 * @return {Array<any>[]} An array containing the fetched data as the first element and any error that occurred as the second element.
 */
export function useFetch(url: string, loadMore?: boolean, refetch?: boolean): Array<any>[] {
  const [data, setData] = React.useState<Array<any>>([]);
  const [error, setError] = React.useState<Array<any>>([]);

  useEffect(() => {
    async function fetchData() {
      try {
        const res = await fetch(url);
        const json = await res.json();

        if (loadMore) {
          setData((prevData) => [...prevData, ...json]);
        } else {
          setData(json);
        }
      } catch (err) {
        setError(err);
      }
    }

    fetchData();
  }, [url, refetch]);

  return [data, error];
}
