/**
 * Formats the modified date to a time string in the format "HH:MM".
 *
 * @param {string} modified - The modified date string.
 * @return {string} The formatted time string.
 */
export function timeFormat(modified: string): string {
  // Format modified date to 07:05
  const date = new Date(modified);
  const hours = date.getHours();
  const minutes = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes();

  return `${hours}:${minutes}`;
}
