import React, { useEffect } from 'react';
import { timeFormat } from './utility';
import { useFetch } from './hooks';

type Props = {
  category: string;
  perPage: number;
};

type MinutaPoMinuteType = {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  modified: string;
};

export default function MinutaPoMinute({ category, perPage }: Props) {
  const [offset, setOffset] = React.useState(0);
  const [loadMoreButton, setLoadMoreButton] = React.useState(true);

  const [data, error] = useFetch(
    `/wp-json/wp/v2/minuta-po-minute?per_page=${perPage}&order=desc&orderby=date&status=publish&minuta-po-minute-category=${category}&offset=${offset}`,
    true
  ) as [MinutaPoMinuteType[], any];

  if (error && error.length > 0) {
    return <div>Nastala chyba. Skúste to neskôr alebo nás kontaktujte.</div>;
  }

  if (!data) {
    return <div>Nahrávam ...</div>;
  }

  let handleLoadClick = () => {
    setOffset((prevOffset) => prevOffset + perPage);
  };

  const searchInRendered = (data: MinutaPoMinuteType[], search: string) => {
    return data.filter((item) => {
      return item.title.rendered.toLowerCase().includes(search) || item.content.rendered.toLowerCase().includes(search);
    });
  };

  useEffect(() => {
    if (data.length > 0) {
      if (data.length <= offset) {
        setLoadMoreButton(false);
      } else {
        setLoadMoreButton(true);
      }
    }
  }, [data]);

  useEffect(() => {
    if (
      searchInRendered(data, 'https://platform.twitter.com/widgets.js') &&
      !document.querySelector('script[src="https://platform.twitter.com/widgets.js"]')
    ) {
      const script = document.createElement('script');
      script.src = 'https://platform.twitter.com/widgets.js';
      script.async = false;
      document.body.appendChild(script);
    }
  }, [data]);

  return (
    <div className="mpm-flex mpm-flex-col">
      {data.map((item: any) => (
        <div
          key={item.id}
          className="mpm-first:border-t mpm-flex mpm-flex-col mpm-border-b mpm-border-solid mpm-border-[#DBDBDB] mpm-py-6"
        >
          <div className="mpm-flex mpm-gap-4">
            <time className="mpm-text-base mpm-font-medium mpm-text-[var(--accent-color)]">
              {timeFormat(item.date)}
            </time>
            <div className="mpm-flex mpm-w-full mpm-flex-col">
              <strong
                dangerouslySetInnerHTML={{ __html: item.title.rendered }}
                className="mpm-text-[var(--headline-color)]"
              ></strong>
              <div
                dangerouslySetInnerHTML={{ __html: item.content.rendered }}
                className="mpm-text-[var(--text-color)]"
              ></div>
            </div>
          </div>
        </div>
      ))}

      {/* Load more button */}
      {loadMoreButton ? (
        <div className="mpm-mt-6 mpm-flex mpm-justify-center">
          <button
            className="mpm-flex mpm-items-center mpm-justify-center mpm-rounded-md mpm-border mpm-border-solid mpm-border-[#A91F24] mpm-bg-transparent mpm-px-4 mpm-py-2 mpm-text-base mpm-font-medium mpm-text-[#A91F24] mpm-transition-colors mpm-duration-300 mpm-ease-in-out hover:mpm-bg-[#A91F24] hover:mpm-text-white"
            onClick={handleLoadClick}
          >
            Načítať viac
          </button>
        </div>
      ) : (
        <div className="mpm-mt-6 mpm-flex mpm-justify-center">
          <p className="mpm-text-base mpm-font-medium mpm-text-[#A91F24]">Žiadne ďalšie príspevky</p>
        </div>
      )}
    </div>
  );
}
