<?php

namespace Creanet\MinutaPoMinute\Taxonomies;

use Creanet\MinutaPoMinute\Core\Taxonomy;

class MinutaPoMinuteCategory extends Taxonomy {

	public function getArgs(): array {
		return [
			'public'            => true,
			'hierarchical'      => true,
			'show_admin_column' => true,
			'rewrite'           => [
				'slug' => 'minuta-po-minute-category',
			],
			'show_in_rest'      => true,
			'sort'              => true,
		];
	}

	public function getTaxonomyName(): string {
		return 'Kategória - Minúta po minúte';
	}

	public function getPostType(): string {
		return 'minuta-po-minute';
	}

	public function getTaxonomy(): string {
		return 'minuta-po-minute-category';
	}

}
