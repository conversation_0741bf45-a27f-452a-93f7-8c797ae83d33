<?php

namespace Creanet\MinutaPoMinute\Core;

abstract class Taxonomy {


	final public function register(): void {
		$this->register_taxonomy();
	}

	public function register_taxonomy(): void {
		$args = $this->getArgs();

		$args['labels'] = $this->get_labels();

		register_taxonomy( $this->getTaxonomy(), $this->getPostType(), $args );
	}

	abstract public function getArgs(): array;

	private function get_labels(): array {
		return [
			'name'                  => $this->getTaxonomyName(),
			'singular_name'         => $this->getTaxonomyName(),
			'search_items'          => __( 'Hľadať ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'popular_items'         => __( 'Populárne ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'all_items'             => __( 'Všetky ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'parent_item'           => __( 'Rodičovský ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'parent_item_colon'     => __( 'Rodičovský ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'edit_item'             => __( 'Upraviť ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'update_item'           => __( 'Aktualizovať ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'add_new_item'          => __( 'Pridať nový ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'new_item_name'         => __( 'Nový ' . $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'add_or_remove_items'   => __( 'Pridať alebo odstrániť ' . $this->getTaxonomyName(),
				'creanet-minuta-po-minute' ),
			'choose_from_most_used' => __( 'Vybrať z najpoužívanejších ' . $this->getTaxonomyName(),
				'creanet-minuta-po-minute' ),
			'not_found'             => __( 'Nenájdené', 'creanet-minuta-po-minute' ),
			'menu_name'             => __( $this->getTaxonomyName(), 'creanet-minuta-po-minute' ),
			'items_list_navigation' => __( 'Navigácia zoznamu ' . $this->getTaxonomyName(),
				'creanet-minuta-po-minute' ),
		];
	}

	abstract public function getTaxonomyName(): string;

	abstract public function getTaxonomy(): string;

	abstract public function getPostType(): string;
}
