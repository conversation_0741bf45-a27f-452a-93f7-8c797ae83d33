<?php

namespace Creanet\MinutaPoMinute\Core;

abstract class Post {

	final public function register(): void {
		$this->register_post_type();
	}

	public function register_post_type(): void {
		$args = $this->getArgs();


		$args['labels'] = $this->get_labels();

		register_post_type( $this->getPostType(), $args );
	}

	abstract public function getArgs(): array;

	/**
	 * Get the labels for a custom post type.
	 *
	 * This method retrieves an array of labels for a custom post type.
	 *
	 * @return array The labels for the custom post type.
	 */
	private function get_labels(): array {
		return [
			'name'                  => $this->getPostName(),
			'singular_name'         => $this->getPostName(),
			'add_new'               => __( 'Pridať nový', 'creanet-minuta-po-minute' ),
			'add_new_item'          => __( 'Pridať nový ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'edit_item'             => __( 'Upraviť ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'new_item'              => __( 'Nový ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'view_item'             => __( 'Zobraziť ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'view_items'            => __( 'Zobraziť ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'search_items'          => __( 'Hľadať ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'not_found'             => __( 'Nenájdené', 'creanet-minuta-po-minute' ),
			'not_found_in_trash'    => __( 'Nenájdené v koši', 'creanet-minuta-po-minute' ),
			'parent_item_colon'     => __( 'Rodičovský ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'all_items'             => __( 'Všetky ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'archives'              => __( 'Archív ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'attributes'            => __( 'Atribúty ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'insert_into_item'      => __( 'Vložiť do ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'uploaded_to_this_item' => __( 'Nahrané do ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
			'featured_image'        => __( 'Náhľadový obrázok ' . $this->getPostName(), 'creanet-minuta-po-minute' ),
		];
	}

	abstract public function getPostName(): string;

	abstract public function getPostType(): string;
}
