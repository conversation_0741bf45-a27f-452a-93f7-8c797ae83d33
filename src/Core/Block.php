<?php

namespace Creanet\MinutaPoMinute\Core;

abstract class Block {
	final public function register(): void {
		$this->register_block();
	}

	public function register_block(): void {
		if ( function_exists( 'acf_register_block' ) ) {
			acf_register_block( $this->getArgs() );
		}

		if ( function_exists( 'acf_add_local_field_group' ) ) {
			acf_add_local_field_group( $this->getFields() );
		}
	}

	public function getArgs(): array {
		return [
			'name'            => $this->getBlockName(),
			'title'           => __( $this->getBlockTitle() ),
			'description'     => __( $this->getBlockDescription() ),
			'render_callback' => [ $this, 'render' ],
			'category'        => $this->getBlockCategory(),
			'icon'            => $this->getBlockIcon(),
			'keywords'        => $this->getBlockKeywords(),
			'mode'            => 'edit',
		];
	}

	abstract public function getBlockName(): string;

	abstract public function getBlockTitle(): string;

	public function getBlockDescription(): string {
		return '';
	}

	public function getBlockCategory(): string {
		return 'common';
	}

	public function getBlockIcon(): string {
		return 'admin-comments';
	}

	public function getBlockKeywords(): array {
		return [];
	}

	public function getFields(): array {
		return [
			'key'      => 'group_' . $this->getBlockName(),
			'title'    => __( $this->getBlockTitle() ),
			'fields'   => $this->getFieldsArray(),
			'location' => [
				[
					[
						'param'    => 'block',
						'operator' => '==',
						'value'    => 'acf/' . $this->getBlockName(),
					],
				],
			],
		];
	}

	public function getFieldsArray(): array {
		return [];
	}

	abstract public function render( $block, $content = '', $is_preview = false, $post_id = 0 ): void;


}
