<?php

namespace Creanet\MinutaPoMinute\Posts;

use Creanet\MinutaPoMinute\Core\Post;

class MinutaPoMinutePost extends Post {

	public function getPostType(): string {
		return 'minuta-po-minute';
	}

	public function getPostName(): string {
		return 'Minúta po minúte';
	}

	public function getArgs(): array {
		return [
			'public'              => true,
			'exclude_from_search' => true,
			'show_in_nav_menus'   => false,
			'show_in_rest'        => true,
			'supports'            => [ 'title', 'editor', 'excerpt', 'custom-fields' ],
			'menu_icon'           => 'dashicons-clock',
			'has_archive'         => false,
			'rewrite'             => [
				'slug' => 'minuta-po-minute',
			],
			'taxonomies'          => [
				'minuta-po-minute-category',
			],
			
		];
	}
}
