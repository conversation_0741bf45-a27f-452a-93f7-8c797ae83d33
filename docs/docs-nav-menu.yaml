- section:
    name: Getting Started
    items:
      - uri: /docs/introduction/
        label: Introduction
      - uri: /docs/quick-start/
- section:
    name: Beginner Guides
    items:
      - uri: /docs/intro-to-graphql/
      - uri: /docs/intro-to-wordpress/
      - uri: /docs/interacting-with-wpgraphql/
      - uri: /docs/build-your-first-wpgraphql-extension/
      - uri: /docs/wpgraphql-vs-wp-rest-api/
- section:
    name: Using WPGraphQL
    items:
      - uri: /docs/posts-and-pages/
      - uri: /docs/custom-post-types/
      - uri: /docs/categories-and-tags/
      - uri: /docs/custom-taxonomies/
      - uri: /docs/media/
      - uri: /docs/menus/
      - uri: /docs/settings/
      - uri: /docs/users/
      - uri: /docs/comments/
      - uri: /docs/plugins/
      - uri: /docs/themes/
      - uri: /docs/widgets/
- section:
    name: Dig Deeper
    items:
      - uri: /docs/wpgraphql-concepts/
      - uri: /docs/wordpress-as-an-application-data-graph/
      - uri: /docs/wpgraphql-request-lifecycle/
      - uri: /docs/default-types-and-fields/
      - uri: /docs/connections/
      - uri: /docs/interfaces/
      - uri: /docs/wpgraphql-mutations/
      - uri: /docs/graphql-resolvers/
      - uri: /docs/performance/
      - uri: /docs/security/
      - uri: /docs/authentication-and-authorization/
      - uri: /docs/hierarchical-data/
      - uri: /docs/debugging/
      - uri: /docs/using-data-from-custom-database-tables/
      - uri: /docs/use-with-php/
- section:
    name: Community
    items:
      - uri: /docs/contributing/
        label: How to Contribute
      - uri: /docs/testing/
      - uri: /docs/upgrading/
      - uri: /docs/faqs/
