importScripts( 'https://www.gstatic.com/firebasejs/12.0.0/firebase-app-compat.js' );
importScripts( 'https://www.gstatic.com/firebasejs/12.0.0/firebase-messaging-compat.js' );

const FIREBASE_CONFIG = {
    apiKey: 'AIzaSyDlUw3RFELP4Uwvlaax5ydDIlyzXsrknyk',
    appId: '1:511777584857:web:168974c3725d03d012b0c8',
    messagingSenderId: '511777584857',
    projectId: 'spravy-rtvs-f830a',
};

firebase.initializeApp( FIREBASE_CONFIG );

const messaging = firebase.messaging();

messaging.setBackgroundMessageHandler((payload) => {
    console.log( '[firebase-messaging-sw.js] Received background message:', payload );

    // Validate payload structure
    if ( !payload ) {
        console.error( '[firebase-messaging-sw.js] Empty payload received' );
        return;
    }

    const promiseChain = self.clients
        .matchAll( {
            type: "window",
            includeUncontrolled: true
        } )
        .then( windowClients => {
            windowClients.forEach( ( windowClient ) => {
                windowClient.postMessage( {
                    message: '💬',
                    time: new Date().toString(),
                    payload: payload
                } );
            } );
        } )
        .then( () => {
            // Improved payload structure handling
            const title = payload.notification?.title || payload.title || 'Správy RTVS';
            const body = payload.notification?.body || payload.body || 'Máte novú správu';
            const icon = payload.notification?.icon || payload.icon || '/wp-content/themes/wprig-k-veci-theme/assets/images/icon-192x192.png';
            
            const options = {
                body: body,
                icon: icon,
                badge: '/wp-content/themes/wprig-k-veci-theme/assets/images/badge-72x72.png',
                tag: 'firebase-notification',
                requireInteraction: true,
                actions: [
                    {
                        action: 'open',
                        title: 'Otvoriť'
                    },
                    {
                        action: 'close',
                        title: 'Zavrieť'
                    }
                ]
            };

            // Enhanced URL handling with validation
            if ( payload.data && payload.data.url ) {
                try {
                    // Validate URL format
                    new URL( payload.data.url );
                    options.data = {
                        url: payload.data.url,
                        ...payload.data // Include all custom data
                    };
                    console.log( '[firebase-messaging-sw.js] 🔗 Notification contains URL:', payload.data.url );
                } catch ( error ) {
                    console.error( '[firebase-messaging-sw.js] Invalid URL in notification:', payload.data.url, error );
                    // Still show notification but without URL data
                    options.data = payload.data;
                }
            } else {
                // Store all custom data even without URL
                options.data = payload.data || {};
            }

            return self.registration.showNotification( title, options );
        } )
        .catch( ( error ) => {
            console.error( '[firebase-messaging-sw.js] Error showing notification:', error );
        } );

    return promiseChain;
} );

// Enhanced notification click handler with improved fallback logic
self.addEventListener( 'notificationclick', ( event ) => {
    console.log( '[firebase-messaging-sw.js] Notification click received.', event );
    
    event.notification.close();

    // Handle different actions
    if ( event.action === 'close' ) {
        console.log( '[firebase-messaging-sw.js] User closed notification' );
        return; // User explicitly closed the notification
    }

    // Handle 'open' action or default click (no action)
    if ( event.action === 'open' || !event.action ) {
        // Check if the notification has custom URL data
        if ( event.notification.data && event.notification.data.url ) {
            const urlToOpen = event.notification.data.url;
            
            // Validate URL before opening
            try {
                new URL( urlToOpen );
                console.log( '[firebase-messaging-sw.js] 🔗 Opening URL from notification:', urlToOpen );
            } catch ( error ) {
                console.error( '[firebase-messaging-sw.js] Invalid URL in notification data:', urlToOpen, error );
                // Fallback to opening application
                return handleFallbackAction( event );
            }
            
            // Try to focus existing window with the URL or open a new one
            event.waitUntil(
                self.clients.matchAll( { type: 'window', includeUncontrolled: true } )
                    .then( ( windowClients ) => {
                        // Check if there's already a window/tab open with the target URL
                        for ( let i = 0; i < windowClients.length; i++ ) {
                            const client = windowClients[i];
                            if ( client.url === urlToOpen && 'focus' in client ) {
                                console.log( '[firebase-messaging-sw.js] Focusing existing window with URL:', urlToOpen );
                                return client.focus();
                            }
                        }
                        
                        // If no existing window found, open a new one
                        if ( self.clients.openWindow ) {
                            console.log( '[firebase-messaging-sw.js] Opening new window with URL:', urlToOpen );
                            return self.clients.openWindow( urlToOpen );
                        }
                    } )
                    .catch( ( error ) => {
                        console.error( '[firebase-messaging-sw.js] Error opening URL:', error );
                        // Fallback to opening application
                        return handleFallbackAction( event );
                    } )
            );
        } else {
            // No URL provided - fallback to opening/focusing application
            console.log( '[firebase-messaging-sw.js] No URL in notification, using fallback action' );
            handleFallbackAction( event );
        }
    }
} );

// Fallback function to handle opening/focusing the application
function handleFallbackAction( event ) {
    event.waitUntil(
        self.clients.matchAll( { type: 'window', includeUncontrolled: true } )
            .then( ( clientList ) => {
                console.log( '[firebase-messaging-sw.js] Looking for existing application windows, found:', clientList.length );
                
                // Try to focus an existing window with the application
                for ( const client of clientList ) {
                    if ( client.url.includes( self.location.origin ) && 'focus' in client ) {
                        console.log( '[firebase-messaging-sw.js] Focusing existing application window:', client.url );
                        return client.focus();
                    }
                }
                
                // If no existing window found, open the application
                if ( self.clients.openWindow ) {
                    console.log( '[firebase-messaging-sw.js] Opening new application window' );
                    return self.clients.openWindow( '/' );
                }
            } )
            .catch( ( error ) => {
                console.error( '[firebase-messaging-sw.js] Error in fallback action:', error );
            } )
    );
}

