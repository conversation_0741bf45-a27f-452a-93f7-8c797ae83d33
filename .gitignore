# Ignore everything in the root except the "wp-content", "k-veci-navody" directory.
/*
!wp-content/
!player/
!firebase-messaging-sw.js
!image.php
!/sonar-project.properties
!googleab1edfbcc89d62bc.html

# Ignore all files starting with .
.*

# track this file .gitignore
!.gitignore

# track this file .gitlab-ci.yml
!.gitlab-ci.yml

# track .editorconfig file
!.editorconfig

# track readme.md in the root
!readme.md

# Ignore all files that start with ~
~*

# Ignore OS generated files
ehthumbs.db
Thumbs.db
.DS_Store

# Ignore log files and databases
*.log
*.sql
*.sqlite

# Ignore compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Ignore packaged files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Ignore everything in the "wp-content" directory, except:
# "mu-plugins" directory
# "plugins" directory
# "themes" directory
wp-content/*
!wp-content/mu-plugins/
!wp-content/plugins/k-veci*
!wp-content/plugins/creanet*
!wp-content/languages/
!wp-content/themes/

# Ignore these plugins from the core
wp-content/plugins/hello.php
wp-content/plugins/akismet/
wp-content/plugins/all-in-one-wp-security-and-firewall-b/

# Ignore specific themes
wp-content/themes/twenty*/
wp-content/themes/k-veci-theme/

# Ignore npm dependency directories
node_modules/

# Ignore "/archiv" folder
archiv
