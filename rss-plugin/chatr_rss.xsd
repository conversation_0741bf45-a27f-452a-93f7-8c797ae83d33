<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:complexType name="image">
        <xs:attribute name="src" type="xs:string"></xs:attribute>
        <xs:attribute name="height" type="xs:integer"></xs:attribute>
        <xs:attribute name="width" type="xs:integer"></xs:attribute>
        <xs:attribute name="description" type="xs:string"></xs:attribute>
    </xs:complexType>

    <xs:element name="image" type="image"/>
    <xs:element name="gallery">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" name="image" type="image" />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="tags">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" name="tag" type="xs:string"></xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="created-at" type="xs:date">
        <xs:complexType>
            <xs:attribute name="timestamp" type="xs:integer"></xs:attribute>
        </xs:complexType>
    </xs:element>
    <xs:element name="modified-at" type="xs:date">
        <xs:complexType>
            <xs:attribute name="timestamp" type="xs:integer"></xs:attribute>
        </xs:complexType>
    </xs:element>
    <xs:element name="clicks" type="xs:integer"></xs:element>
    <xs:element name="excerpt" type="xs:string"></xs:element>

</xs:schema>
