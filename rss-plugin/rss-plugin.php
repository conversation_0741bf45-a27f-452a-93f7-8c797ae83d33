<?php
/**
 * <AUTHOR> <<EMAIL>>
 */

/*
Plugin Name: rss-plugin
Description: Chatr.tv rss plugin wordpress >= 4.4
Version:     1.7
Author:      <PERSON><PERSON>
*/

defined( 'ABSPATH' ) or die( 'No script kiddies please!' );

function registerTagsEndpoint() {
	add_rewrite_endpoint( 'chatr_tags', EP_ALL );
}

register_deactivation_hook( __FILE__, 'flush_rewrite_rules' );
register_activation_hook( __FILE__, 'myplugin_flush_rewrites' );

function myplugin_flush_rewrites() {
	// call your CPT registration function here (it should also be hooked into 'init')
	registerTagsEndpoint();
	flush_rewrite_rules();
}

// add rewrite rule for tags endpoint
add_action( 'init', 'registerTagsEndpoint', 1000 );

// send tags as XML if chatr-tags endpoint is visited
add_action( 'template_redirect', function () {
	global $wp_query, $wpdb;

	if ( ! isset( $wp_query->query_vars['chatr_tags'] ) ) {
		return;
	}

	$tags = $wpdb->get_results( "
        SELECT $wpdb->terms.* FROM $wpdb->terms
        JOIN $wpdb->term_taxonomy
            ON $wpdb->term_taxonomy.term_id = $wpdb->terms.term_id
        WHERE $wpdb->term_taxonomy.taxonomy = 'post_tag'
    ", ARRAY_A );

	// load priorities for tags
	header( "Content-Type: application/xhtml+xml; charset=utf-8" );
	echo( "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" );
	echo( "<tags>\n" );

	foreach ( $tags as &$tag ) {
		$priority = $wpdb->get_results( "SELECT * FROM $wpdb->termmeta WHERE term_id = {$tag['term_id']} AND meta_key = 'priority'", ARRAY_A );

		if ( ! $priority || count( $priority ) === 0 ) {
			$priority = 0;
		} else {
			$priority = $priority[0]['meta_value'];
		}

		$tag['priority'] = $priority;
	}

	// sort by priority
	usort( $tags, function ( $a, $b ) {
		if ( $a['priority'] > $b['priority'] ) {
			return - 1;
		} else if ( $a['priority'] < $b['priority'] ) {
			return 1;
		}

		return 0;
	} );

	$sliced = array_slice( $tags, 0, 30 );

	foreach ( $sliced as $tag ) {
		echo(
			"\t<tag name='" . htmlspecialchars( $tag['name'], ENT_XML1 | ENT_QUOTES ) . "' slug='" . htmlspecialchars( $tag['slug'], ENT_XML1 | ENT_QUOTES ) . "' priority='{$tag['priority']}' id='{$tag['term_id']}' />\n"
		);
	}

	echo( "</tags>" );

	exit;
}, 50 );


// add rss2 namespace for chatr
add_action( 'rss2_ns', function () {
	echo 'xmlns:chatr="http://www.w3.org/2001/XMLSchema-instance" chatr:schemaLocation="http://www.w3schools.com http://rss-plugin.creanet.sk/chatr_rss.xsd"';
} );

// register action hook to add image of post
add_action( 'rss2_item', function () {
	global $post;

	if ( has_post_thumbnail( $post->ID ) ) {
		$photoId   = get_post_thumbnail_id( $post->ID );
		$original  = wp_get_attachment_image_src( $photoId, 'full' );
		$thumbnail = wp_get_attachment_image_src( $photoId, 'medium' );

		if ( ! $thumbnail ) {
			return; // do nothing
		}

		list( $src, $width, $height ) = $thumbnail;
		$imageData = get_post( $photoId );

		echo( "\t<chatr:image src='{$src}' original='{$original[0]}' height='{$height}' width='{$width}' description='{$imageData->post_content}'></chatr:image>\n" );
	}
} );

add_action( 'rss2_item', function () {
	global $post;

	$photos = get_children( array(
		'post_parent'    => $post->ID,
		'post_status'    => 'inherit',
		'post_type'      => 'attachment',
		'post_mime_type' => 'image',
		'order'          => 'ASC',
		'orderby'        => 'menu_order ID'
	) );

	$loadedPhotos = [];

	foreach ( $photos as $photo ) {
		$thumbnail      = wp_get_attachment_image_src( $photo->ID, 'medium' );
		$original       = wp_get_attachment_image_src( $photo->ID, 'full' );
		$imageData      = get_post( $photo->ID );
		$loadedPhotos[] = [ $thumbnail, $imageData, $original ];
	}

	$galleries = get_post_galleries( $post, false );

	foreach ( $galleries as $gallery ) {
		$photoIds = explode( ',', $gallery['ids'] );

		foreach ( $photoIds as $photoId ) {
			$thumbnail      = wp_get_attachment_image_src( $photoId, 'medium' );
			$original       = wp_get_attachment_image_src( $photoId, 'full' );
			$imageData      = get_post( $photoId );
			$loadedPhotos[] = [ $thumbnail, $imageData, $original ];
		}
	}

	if ( count( $loadedPhotos ) > 0 ) {
		echo "\t<chatr:gallery>\n";

		foreach ( $loadedPhotos as $photo ) {
			echo "\t\t<chatr:image src='{$photo[0][0]}' original='{$photo[2][0]}' description='{$photo[1]->post_content}' width='{$photo[0][1]}' height='{$photo[0][2]}'></chatr:image>\n";
		}

		echo "\t</chatr:gallery>\n";
	}
} );

// register action hook to add tags
add_action( 'rss2_item', function () {
	global $post;

	$tags = wp_get_post_tags( $post->ID );

	echo "\t<chatr:tags>\n";

	foreach ( $tags as $tag ) {
		echo "\t\t<chatr:tag slug='{$tag->slug}'><![CDATA[{$tag->name}]]></chatr:tag>\n";
	}

	echo "\t</chatr:tags>\n";
} );

// register action hook to add videos
add_action( 'rss2_item', function () {
	global $post;

	$content = do_shortcode( apply_filters( 'the_content', $post->post_content ) );
	$videos  = get_media_embedded_in_content( $content, 'video' );

	foreach ( $videos as $video ) {
		$poster = '';
		preg_match( '/<video[^>]+poster="([^"]+)"/', $video, $posterMatch );
		preg_match( '/<a[^>]+href="([^"]+)"/', $video, $videoMatch );

		if ( is_array( $posterMatch ) && count( $posterMatch ) > 1 ) {
			$poster = $posterMatch[1];
		}

		if ( isset( $videoMatch[1] ) ) {
			echo "\t<chatr:video src='{$videoMatch[1]}' poster='{$poster}'></chatr:video>\n";
		}
	}
} );

// register action hook to add audio files
add_action( 'rss2_item', function () {
	global $post;

	$content     = do_shortcode( apply_filters( 'the_content', $post->post_content ) );
	$audio_files = get_media_embedded_in_content( $content, 'audio' );

	foreach ( $audio_files as $audio ) {
		if ( preg_match( '#\[audio\s*.*?\]#s', $audio, $matches ) && preg_match( '/"([^"]+)"/', $matches[0], $m ) ) {
			echo "\t<chatr:audio src='{$audioMatch[1]}'></chatr:audio>\n";
		}
	}
} );

// register action hook to add excerpt
add_action( 'rss2_item', function () {
	global $post;

	$excerpt = get_the_excerpt();

	if ( ! $excerpt ) {
		return;
	}

	// strip anchor tags
	$excerpt = preg_replace( '/(<[aA][^>]+>[^<>].*<\/[aA]>)/', '', $excerpt );
	// no html entities
	$excerpt = html_entity_decode( $excerpt );

	echo "\t<chatr:excerpt><![CDATA[{$excerpt}]]></chatr:excerpt>\n";
} );

// show modified and created time of post
add_action( 'rss2_item', function () {
	global $post;

	$createdAt      = get_post_time( 'U', false, $post );
	$createdAtText  = get_post_time( 'c', false, $post );
	$modifiedAt     = get_post_modified_time();
	$modifiedAtText = get_post_modified_time( 'c' );

	echo "\t<chatr:created-at timestamp='{$createdAt}'>{$createdAtText}</chatr:created-at>\n";
	echo "\t<chatr:modified-at timestamp='{$modifiedAt}'>{$modifiedAtText}</chatr:modified-at>\n";
} );


// count clicks
add_action( 'wp', function () {
	global $post;

	if ( is_single() || is_page() ) {
		if ( isset( $post->ID ) ) {
			$views         = intval( get_post_meta( $post->ID, '_post_views', true ) );
			$views_updated = $views + 1;
			update_post_meta( $post->ID, '_post_views', $views_updated, $views );
		}
	}
} );

add_action( 'rss2_item', function () {
	global $post;

	$clicks = intval( get_post_meta( $post->ID, '_post_views', true ) );

	echo "\t<chatr:clicks>{$clicks}</chatr:clicks>\n";
} );


function get_the_author_fullname( $author_id ) {
	return trim( get_the_author_meta( 'first_name', $author_id ) . ' ' . get_the_author_meta( 'last_name', $author_id ) );
}

// add priority to tag as form field
add_action( 'edit_tag_form_fields', function ( $tag ) {
	$termId   = $tag->term_id; // Get the ID of the term you're editing
	$priority = (int) get_term_meta( $termId, 'priority', true );

	echo <<<'FORM_FIELD'
<tr class="form-field">
    <th valign="top" scope="row">
        <label for="tag-priority">Priorita značky</label>
    </th>
    <td>
        <input type="number" name="tag-priority" id="tag-priority" value="{$priority}">
        <p class="description">0 - nezobrazuje sa v app, 1 - najnižšia priorita, 9 - najvyššia priorita</p>
    </td>
</tr>
FORM_FIELD;
} );

// add priority to tag as form field
add_action( 'add_tag_form_fields', function () {
	echo <<<'FORM_FIELD'
<tr class="form-field">
    <th valign="top" scope="row">
        <label for="tag-priority">Priorita značky</label>
    </th>
    <td>
        <input type="number" name="tag-priority" id="tag-priority" value="0">
        <p class="description">0 - nezobrazuje sa v app, 1 - najnižšia priorita, 9 - najvyššia priorita</p>
    </td>
</tr>
FORM_FIELD;
} );

// save priority to db
add_action( 'edited_terms', function ( $termId ) {
	if ( isset( $_POST['tag-priority'] ) ) {
		// save priority to meta
		update_term_meta( $termId, 'priority', $_POST['tag-priority'] );
	}
} );

// save priority to db
add_action( 'created_term', function ( $termId ) {
	if ( isset( $_POST['tag-priority'] ) ) {
		// save priority to meta
		add_term_meta( $termId, 'priority', $_POST['tag-priority'] );
	}
} );

// add columns for tag priority
add_filter( 'manage_edit-post_tag_columns', function ( $columns ) {
	$columns['priority'] = 'Priority';

	return $columns;
} );


add_filter( 'manage_edit-post_tag_sortable_columns', function ( $columns ) {
	$columns['priority'] = 'priority';

	return $columns;
} );


add_filter( 'manage_post_tag_custom_column', function ( $content, $column, $termId ) {
	$priority = (int) get_term_meta( $termId, 'priority', true );

	return $priority;
}, 10, 3 );

add_filter( 'get_terms', function ( $terms, $taxonomies, $args ) {
	if ( $args['orderby'] != 'priority' ) {
		return $terms;
	}

	$empty = false;

	foreach ( $terms as $term ) {
		if ( is_object( $term ) ) {
			if ( $priority = get_term_meta( $term->term_id, 'priority', true ) ) {
				$term->priority = (int) $priority;
			} else {
				$term->priority = (int) 0;
			}
		} else {
			$empty = true;
		}
	}

	// marge ordered and unordered terms
	// now sort them
	if ( ! $empty && count( $terms ) > 0 ) {
		if ( $args['orderby'] == 'priority' && $args['order'] == 'desc' ) {
			usort( $terms, function ( $a, $b ) {
				if ( $a->priority == $b->priority ) {
					return 0;
				} else if ( $a->priority > $b->priority ) {
					return - 1;
				}

				return 1;
			} );
		} else {
			usort( $terms, function ( $a, $b ) {
				if ( $a->priority == $b->priority ) {
					return 0;
				} else if ( $a->priority < $b->priority ) {
					return - 1;
				}

				return 1;
			} );
		}
	}

	return $terms;
}, 10, 3 );

