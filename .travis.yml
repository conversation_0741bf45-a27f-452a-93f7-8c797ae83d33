sudo: required

services:
  - docker

language: php

notifications:
  email:
    on_success: never
    on_failure: never

branches:
  only:
    - master
    - develop

cache:
  apt: true

## Set the global environment variables
matrix:
  include:
    - php: 7.2
      env: WP_VERSION=5.3.0 PHP_VERSION=7.1
    - php: 7.2
      env: WP_VERSION=5.3.2 PHP_VERSION=7.2
    - php: 7.3
      env: WP_VERSION=5.3.2 PHP_VERSION=7.3
    - php: 7.4
      env: WP_VERSION=5.3.2 PHP_VERSION=7.4 COVERAGE=true LINT_SCHEMA=true
    - php: 7.4
      env: WP_VERSION=5.3.2 PHP_VERSION=7.4 PHPCS=true
  allow_failures:
    - env: WP_VERSION=5.3.2 PHP_VERSION=7.4 PHPCS=true

before_install:
  - sudo rm /usr/local/bin/docker-compose
  - curl -L https://github.com/docker/compose/releases/download/1.24.1/docker-compose-`uname -s`-`uname -m` > docker-compose
  - chmod +x docker-compose
  - sudo mv docker-compose /usr/local/bin
  - |
    # Remove Xdebug for a huge performance increase:
    if [ -f ~/.phpenv/versions/$(phpenv version-name)/etc/conf.d/xdebug.ini ]; then
      phpenv config-rm xdebug.ini
    else
      echo "xdebug.ini does not exist"
    fi

before_script:
  # Install PHP CodeSniffer and WPCS.
  - |
    if [[ "$PHPCS" == "true" ]]; then
      composer install

      phpenv rehash
    fi

script:
  - ./run-docker-tests.sh 'wpunit'
  - ./run-docker-tests.sh 'functional'
  - ./run-docker-tests.sh 'acceptance'
  - if [[ "$PHPCS" == "true" ]]; then composer check-cs; fi

after_success:
  # Upload coverage to codecov
  - |
    if [[ "${COVERAGE}" == 'true' ]]; then
      bash <(curl -s https://codecov.io/bash)

      composer self-update # This should not be required, but it is always a good practice to update composer
      composer install
      composer require php-coveralls/php-coveralls
      travis_retry php vendor/bin/php-coveralls
    fi
  # Install GraphQL Schema Linter
  # Move to the WordPress Install
  # Generate the Static Schema
  # Lint the Schema
  - |
    if [ $LINT_SCHEMA == true ]; then
      npm install -g graphql-schema-linter
      cd $WP_CORE_DIR
      wp graphql generate-static-schema
      cd $WP_CORE_DIR/wp-content/plugins/wp-graphql
      graphql-schema-linter ./schema.graphql
    fi
