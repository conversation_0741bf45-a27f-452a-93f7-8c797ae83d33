{"name": "creanet-minuta-po-minute", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npm run build:react && npm run build:tailwind", "build:react": "webpack --config webpack.config.js --mode development", "build:production": "webpack --config webpack.config.js --mode production && npm run build:tailwind", "dev": "nodemon -w src -e ts,tsx,php,css --exec \"npm run build\"", "build:tailwind": "tailwindcss -c ./tailwind.config.js -i src/assets/css/app.scss -o dist/bundlem.css"}, "repository": {"type": "git", "url": "*********************:k-veci/creanet-minuta-po-minute.git"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/core": "^7.22.20", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.68.0", "sass-loader": "^13.3.2"}, "devDependencies": {"@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "babel-loader": "^9.1.3", "nodemon": "^3.0.1", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.3.3", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "prettier": {"arrowParens": "always", "endOfLine": "auto", "htmlWhitespaceSensitivity": "css", "printWidth": 120, "quoteProps": "as-needed", "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "plugins": ["prettier-plugin-tailwindcss"]}}