stages:
  - Build
  - Release
  - Analyze

#
# Build
#

Build:
  stage: Build
  image: node:18
  script:
    - cd wp-content/themes/wprig-k-veci-theme
    - npm ci
    - npm run bundle
  artifacts:
    paths:
      - wp-content/themes/k-veci-theme

#
# Release
#

Publish:
  stage: Release
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  rules:
    - if: $CI_COMMIT_TAG # Run this job when a tag is created
  script:
    - echo "Creating release for $CI_COMMIT_TAG"
  release:
    name: 'Release $CI_COMMIT_TAG'
    tag_name: '$CI_COMMIT_TAG'
    description: 'Release $CI_COMMIT_TAG'
    assets:
      links:
        - name: 'Theme'
          url: '${CI_PROJECT_URL}/-/jobs/artifacts/${CI_COMMIT_TAG}/download?job=Build'

# Upload the theme

UploadTheme:
  stage: Release
  allow_failure: true
  image: alpine:latest
  needs:
    - job: Build
      artifacts: true
  before_script:
    - apk add --no-cache openssh zip
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 -d > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host $SSH_TARGET_SERVER\n\tStrictHostKeyChecking no\n\tPort $SSH_TARGET_PORT\n" >> ~/.ssh/config
  script:
    - export THEME_DIR="wp-content/themes/k-veci-theme"
    - export COMMIT_HASH="$(echo $CI_COMMIT_SHA | cut -c1-8)"
    - cp -r "$THEME_DIR" "theme-$COMMIT_HASH"
    - cd "theme-$COMMIT_HASH" && zip -r "../theme-$COMMIT_HASH.zip" . && cd ..
    - cp "theme-$COMMIT_HASH.zip" theme-latest.zip
    - scp -P $SSH_TARGET_PORT theme-$COMMIT_HASH.zip theme-latest.zip $SSH_TARGET_USER@$SSH_TARGET_SERVER:$SSH_TARGET_DIRECTORY
  only:
    - master

#
# Analyze
#

SonarQube:
  stage: Analyze
  allow_failure: true

  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: ['']

  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar' # Defines the location of the analysis task cache
    GIT_DEPTH: '0' # Tells git to fetch all the branches of the project, required by the analysis task

  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache

  before_script: [] # Nothing

  script:
    - sonar-scanner

  only:
    - master
