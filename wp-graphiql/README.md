# WPGraphiQL: GraphiQL IDE in the WP-Admin

This plugin brings the power of the GraphiQL IDE to the WP-Admin. 

Activate this plugin – with WPGraphQL (https://github.com/wp-graphql/wp-graphql) also active – and you will be able to browse your WPGraphQL schema straight from the WP-Admin. 

Authentication works with your current session, so if you have proper permissions, you can run Mutations and Query 
for data that might be restricted to current users. 

<img src="https://github.com/wp-graphql/wp-graphiql/blob/master/assets/img/wp-graphiql-wp-admin.gif?raw=true" alt="WPGraphiQL - GraphiQL in the WP-Admin">
