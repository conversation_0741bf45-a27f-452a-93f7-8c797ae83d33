{"name": "wp-graphiql", "description": "This plugin provides the GraphiQL IDE as an admin page in WordPress, allowing the GraphQL WPGraphQL schema to be browsed from within WordPress.", "author": "WPGraphQL, Digital First Media, <PERSON>", "homepage": "http://wpgraphql.com", "bugs": {"url": "https://github.com/wp-graphql/wp-graphiql/issues"}, "version": "1.0.0", "private": true, "devDependencies": {"react-scripts": "^1.1.4"}, "dependencies": {"graphiql": "^0.13.2", "graphiql-code-exporter": "^2.0.5", "graphiql-explorer": "^0.4.3", "graphql": "^14.4.2", "react": "^16.8.6", "react-dom": "^16.8.6", "whatwg-fetch": "^3.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}}