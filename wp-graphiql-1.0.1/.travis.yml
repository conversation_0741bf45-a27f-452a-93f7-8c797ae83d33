sudo: false

language: php

notifications:
  email:
    on_success: never
    on_failure: change

branches:
  only:
    - master

cache:
  directories:
    - vendor
    - $HOME/.composer/cache

matrix:
  include:
    - php: 7.1
      env: WP_VERSION=latest
    - php: 7.0
      env: WP_VERSION=latest
    - php: 5.6
      env: WP_VERSION=4.4
    - php: 5.6
      env: WP_VERSION=latest
    - php: 5.6
      env: WP_VERSION=trunk
    - php: 5.6
      env: WP_TRAVISCI=phpcs
    - php: 5.3
      env: WP_VERSION=latest

before_script:
  - export PATH="$HOME/.composer/vendor/bin:$PATH"
  - |
    if [[ ! -z "$WP_VERSION" ]] ; then
      bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
      if [[ ${TRAVIS_PHP_VERSION:0:2} == "5." ]]; then
        composer global require "phpunit/phpunit=4.8.*"
      else
        composer global require "phpunit/phpunit=5.7.*"
      fi
    fi
  - |
    if [[ "$WP_TRAVISCI" == "phpcs" ]] ; then
      composer global require wp-coding-standards/wpcs
      phpcs --config-set installed_paths $HOME/.composer/vendor/wp-coding-standards/wpcs
    fi

script:
  - |
    if [[ ! -z "$WP_VERSION" ]] ; then
      phpunit
      WP_MULTISITE=1 phpunit
    fi
  - |
    if [[ "$WP_TRAVISCI" == "phpcs" ]] ; then
      phpcs --standard=phpcs.ruleset.xml $(find . -name '*.php')
    fi
